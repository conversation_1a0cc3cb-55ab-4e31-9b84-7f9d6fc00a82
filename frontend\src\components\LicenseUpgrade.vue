<template>
  <a-modal
    v-model:visible="visible"
    title="升级到专业版"
    :width="500"
    :mask-closable="false"
    @ok="handleUpgrade"
    @cancel="handleCancel"
    :ok-loading="upgrading"
    ok-text="立即升级"
    cancel-text="取消"
  >
    <div class="upgrade-content">
      <!-- 版本对比简要说明 -->
      <div class="version-info">
        <div class="current-version">
          <div class="version-badge basic">基础版</div>
          <div class="version-desc">当前版本，功能有限</div>
        </div>
        <div class="upgrade-arrow">
          <icon-arrow-right />
        </div>
        <div class="target-version">
          <div class="version-badge professional">专业版</div>
          <div class="version-desc">解锁所有高级功能</div>
        </div>
      </div>

      <!-- 专业版主要功能 -->
      <div class="features-preview">
        <h4>专业版功能亮点：</h4>
        <div class="feature-list">
          <div class="feature-item">
            <icon-check-circle class="feature-icon" />
            <span>批量设备操作（备份、巡检、删除）</span>
          </div>
          <div class="feature-item">
            <icon-check-circle class="feature-icon" />
            <span>设备导入导出功能</span>
          </div>
          <div class="feature-item">
            <icon-check-circle class="feature-icon" />
            <span>自动备份调度</span>
          </div>
          <div class="feature-item">
            <icon-check-circle class="feature-icon" />
            <span>自定义命令执行</span>
          </div>
          <div class="feature-item">
            <icon-check-circle class="feature-icon" />
            <span>配置文件对比分析</span>
          </div>
          <div class="feature-item">
            <icon-check-circle class="feature-icon" />
            <span>高级报告和统计</span>
          </div>
        </div>
      </div>

      <!-- 授权码输入 -->
      <div class="license-input">
        <a-form :model="form" layout="vertical">
          <a-form-item 
            label="授权码" 
            field="licenseCode"
            :rules="[{ required: true, message: '请输入授权码' }]"
          >
            <a-input
              v-model="form.licenseCode"
              placeholder="请输入专业版授权码"
              :max-length="20"
              allow-clear
              @input="handleInputChange"
            />
            <div class="input-hint">
              <icon-info-circle />
              <span>请输入您购买的专业版授权码</span>
            </div>
          </a-form-item>
        </a-form>
      </div>

      <!-- 错误提示 -->
      <div v-if="errorMessage" class="error-message">
        <icon-exclamation-circle />
        <span>{{ errorMessage }}</span>
      </div>

      <!-- 演示授权码提示 -->
      <div class="demo-hint">
        <a-alert type="info" show-icon>
          <template #icon><icon-lightbulb /></template>
          <div>
            <div><strong>演示授权码：</strong></div>
            <div class="demo-codes">
              <code @click="copyCode('PROF-2024-NETW-TOOL')">PROF-2024-NETW-TOOL</code>
              <code @click="copyCode('DEMO-PROF-TEST-2024')">DEMO-PROF-TEST-2024</code>
            </div>
            <div class="demo-note">点击授权码可快速复制</div>
          </div>
        </a-alert>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { Message } from '@arco-design/web-vue'
import {
  IconArrowRight,
  IconCheckCircle,
  IconInfoCircle,
  IconExclamationCircle,
  IconLightbulb
} from '@arco-design/web-vue/es/icon'
import { enhancedLicenseManager } from '@/utils/enhanced-license'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'success'])

// 响应式数据
const visible = ref(props.modelValue)
const upgrading = ref(false)
const errorMessage = ref('')

const form = reactive({
  licenseCode: ''
})

// 监听props变化
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
})

// 监听visible变化
watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
  if (!newVal) {
    // 关闭时重置表单
    resetForm()
  }
})

// 重置表单
const resetForm = () => {
  form.licenseCode = ''
  errorMessage.value = ''
  upgrading.value = false
}

// 处理输入变化
const handleInputChange = () => {
  errorMessage.value = ''
}

// 复制演示授权码
const copyCode = async (code) => {
  try {
    await navigator.clipboard.writeText(code)
    Message.success('授权码已复制到剪贴板')
    form.licenseCode = code
  } catch (error) {
    console.error('复制失败:', error)
    Message.error('复制失败，请手动复制')
  }
}

// 处理升级
const handleUpgrade = async () => {
  if (!form.licenseCode.trim()) {
    errorMessage.value = '请输入授权码'
    return
  }

  upgrading.value = true
  errorMessage.value = ''

  try {
    // 验证并激活授权
    const result = await enhancedLicenseManager.activateLicense(form.licenseCode)
    
    if (result.success) {
      Message.success(result.message)
      visible.value = false
      emit('success', result)
    } else {
      errorMessage.value = result.error || '授权激活失败'
    }
  } catch (error) {
    console.error('升级失败:', error)
    errorMessage.value = '升级过程中发生错误，请重试'
  } finally {
    upgrading.value = false
  }
}

// 处理取消
const handleCancel = () => {
  visible.value = false
}
</script>

<style scoped>
.upgrade-content {
  padding: 10px 0;
}

.version-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
  padding: 16px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 8px;
}

.current-version,
.target-version {
  text-align: center;
  flex: 1;
}

.version-badge {
  display: inline-block;
  padding: 6px 16px;
  border-radius: 20px;
  font-weight: bold;
  font-size: 14px;
  margin-bottom: 8px;
}

.version-badge.basic {
  background: #e8f4fd;
  color: #1890ff;
  border: 1px solid #91d5ff;
}

.version-badge.professional {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.version-desc {
  color: #666;
  font-size: 12px;
}

.upgrade-arrow {
  margin: 0 20px;
  color: #1890ff;
  font-size: 20px;
}

.features-preview {
  margin-bottom: 24px;
}

.features-preview h4 {
  margin-bottom: 12px;
  color: #333;
  font-size: 16px;
}

.feature-list {
  display: grid;
  gap: 8px;
}

.feature-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid #52c41a;
}

.feature-icon {
  color: #52c41a;
  margin-right: 8px;
  font-size: 16px;
}

.license-input {
  margin-bottom: 16px;
}

.input-hint {
  display: flex;
  align-items: center;
  margin-top: 8px;
  color: #666;
  font-size: 12px;
}

.input-hint .arco-icon {
  margin-right: 4px;
  color: #1890ff;
}

.error-message {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background: #fff2f0;
  border: 1px solid #ffccc7;
  border-radius: 6px;
  color: #ff4d4f;
  margin-bottom: 16px;
}

.error-message .arco-icon {
  margin-right: 8px;
}

.demo-hint {
  margin-top: 16px;
}

.demo-codes {
  margin: 8px 0;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.demo-codes code {
  display: inline-block;
  padding: 4px 8px;
  background: #f6f8fa;
  border: 1px solid #d0d7de;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  cursor: pointer;
  transition: all 0.2s;
}

.demo-codes code:hover {
  background: #e1e8ed;
  border-color: #1890ff;
}

.demo-note {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}
</style>
