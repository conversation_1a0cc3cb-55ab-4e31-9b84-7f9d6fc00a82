from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks, UploadFile, File
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import os
import time
import csv
import io
from datetime import datetime
import netmiko
import json
import subprocess
import platform
import asyncio
import uuid
import threading
from enum import Enum
from concurrent.futures import ThreadPoolExecutor
from sqlalchemy import desc
from sqlalchemy.orm import Session
from database import get_db, Device as DBDevice, Backup as DBBackup, Command as DBCommand, CommandResult as DBCommandResult, License as DBLicense, LicenseVerificationLog as DBLicenseVerificationLog, init_db

app = FastAPI(title="网络设备配置工具", description="用于备份和管理网络设备配置")

# 数据库迁移函数
def migrate_database():
    """
    执行数据库迁移逻辑
    当数据库结构变更时，此函数会处理升级操作
    """
    try:
        # 获取数据库连接
        from sqlalchemy import text
        from database import engine
        
        # 检查vendor和remote_type列是否存在
        with engine.connect() as conn:
            # 获取devices表的列信息
            result = conn.execute(text("PRAGMA table_info(devices)"))
            columns = [row[1] for row in result.fetchall()]
            
            # 如果vendor列不存在，添加它
            if "vendor" not in columns:
                print("添加vendor列")
                conn.execute(text("ALTER TABLE devices ADD COLUMN vendor TEXT"))
                
            # 如果remote_type列不存在，添加它
            if "remote_type" not in columns:
                print("添加remote_type列")
                conn.execute(text("ALTER TABLE devices ADD COLUMN remote_type TEXT DEFAULT 'ssh'"))

            # 如果online_status列不存在，添加它
            if "online_status" not in columns:
                print("添加online_status列")
                conn.execute(text("ALTER TABLE devices ADD COLUMN online_status BOOLEAN DEFAULT 0"))

            # 如果last_status_check列不存在，添加它
            if "last_status_check" not in columns:
                print("添加last_status_check列")
                conn.execute(text("ALTER TABLE devices ADD COLUMN last_status_check TEXT"))

            conn.commit()
            
        print("数据库迁移完成")
    except Exception as e:
        print(f"数据库迁移错误: {str(e)}")
        import traceback
        traceback.print_exc()
        raise

# 应用启动事件
@app.on_event("startup")
def startup_event():
    # 初始化数据库
    init_db()
    
    # 执行数据库迁移
    try:
        migrate_database()
    except Exception as e:
        print(f"数据库迁移失败: {str(e)}")
        # 继续启动应用，但记录错误

# 初始化数据库
init_db()

# 允许跨域请求
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 实际部署时应限制为前端域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 数据模型
class Device(BaseModel):
    id: Optional[int] = None
    name: str
    ip: str
    device_type: str
    username: str
    password: str
    enable_secret: Optional[str] = None
    port: int = 22
    vendor: Optional[str] = None  # 设备厂商，可选
    remote_type: str = "ssh"  # 远程连接类型: ssh 或 telnet
    
    model_config = {
        "from_attributes": True
    }

# 批量操作请求模型
class BatchOperation(BaseModel):
    device_ids: List[int]

class BackupResult(BaseModel):
    device_id: int
    device_name: str
    status: str
    message: str
    timestamp: str
    config: Optional[str] = None

    model_config = {
        "from_attributes": True
    }

# 授权相关数据模型
class LicenseActivationRequest(BaseModel):
    license_code: str
    device_fingerprint: str
    client_info: Optional[Dict[str, Any]] = None

class LicenseVerificationRequest(BaseModel):
    license_code: str
    device_fingerprint: str
    client_info: Optional[Dict[str, Any]] = None

class LicenseResponse(BaseModel):
    success: bool
    license_type: Optional[str] = None
    message: str
    expires_at: Optional[str] = None
    features: Optional[List[str]] = None
    error_code: Optional[str] = None

class LicenseInfo(BaseModel):
    id: int
    license_code: str
    license_type: str
    device_fingerprint: str
    activated_at: str
    expires_at: Optional[str] = None
    is_active: bool
    last_verified_at: str
    verification_count: int

    model_config = {
        "from_attributes": True
    }

# 设备类型映射函数
def map_device_type(vendor: str, remote_type: str) -> str:
    """根据厂商和远程类型映射设备类型"""
    if not vendor:
        raise ValueError("设备厂商不能为空")

    vendor_lower = vendor.lower().strip()

    # 特殊映射规则
    vendor_mapping = {
        # Cisco系列
        'cisco': 'cisco_ios',

        # Huawei系列
        'huawei': 'huawei_vrp',

        # H3C (使用HP Comware)
        'h3c': 'hp_comware',

        # Juniper
        'juniper': 'juniper_junos',

        # Arista
        'arista': 'arista_eos',

        # Fortinet
        'fortinet': 'fortinet',

        # Ruijie
        'ruijie': 'ruijie_os',

        # Dell
        'dell': 'dell_os10',

        # HP/HPE
        'hp': 'hp_comware',

        # Extreme Networks
        'extreme': 'extreme_exos',

        # Brocade
        'brocade': 'brocade_fastiron',

        # Palo Alto
        'paloalto': 'paloalto_panos',

        # F5
        'f5': 'f5_tmsh',

        # Nokia
        'nokia': 'nokia_sros',

        # Avaya
        'avaya': 'avaya_ers',

        # MikroTik
        'mikrotik': 'mikrotik_routeros',

        # Ubiquiti
        'ubiquiti': 'ubiquiti_edge',

        # VyOS
        'vyos': 'vyos',

        # ZTE
        'zte': 'zte_zxros',

        # Zyxel
        'zyxel': 'zyxel_os',

        # Linux
        'linux': 'linux',

        # Generic
        'generic': 'generic'
    }

    # 获取基础设备类型
    base_type = vendor_mapping.get(vendor_lower, vendor_lower)

    # 如果是Telnet连接，某些设备需要特殊处理
    if remote_type.lower() == "telnet":
        # 对于支持telnet的设备，使用特定的telnet类型
        telnet_mapping = {
            'cisco_ios': 'cisco_ios_telnet',
            'huawei_vrp': 'huawei_telnet',
            'hp_comware': 'hp_comware_telnet',
            'juniper_junos': 'juniper_telnet'
        }
        return telnet_mapping.get(base_type, base_type)

    return base_type

# 确保备份目录存在
backup_dir = os.path.join(os.path.dirname(__file__), 'backups')
if not os.path.exists(backup_dir):
    os.makedirs(backup_dir)

# 获取支持的设备类型
@app.get("/api/device-types", response_model=List[Dict[str, str]])
def get_device_types():
    # 获取Netmiko支持的所有设备类型
    platforms = netmiko.platforms
    # 按品牌分组
    device_types = []
    
    # 常用设备类型
    common_platforms = [
        "cisco_ios", "cisco_nxos", "cisco_xr", "cisco_asa",
        "huawei", "huawei_vrpv8", 
        "hp_comware", "hp_procurve",
        "juniper", "juniper_junos",
        "arista_eos",
        "paloalto_panos",
        "fortinet",
        "alcatel_aos",
        "dell_os10", "dell_force10",
        "h3c", "h3c_comware",
        "linux",
        "extreme_exos",
        "mikrotik_routeros",
        "ubiquiti_edge"
    ]
    
    # 先添加常用设备
    for platform in common_platforms:
        if platform in platforms:
            device_types.append({
                "value": platform,
                "label": platform.replace("_", " ").title()
            })
    
    # 再添加其他设备类型
    for platform in platforms:
        if platform not in common_platforms:
            device_types.append({
                "value": platform,
                "label": platform.replace("_", " ").title()
            })
            
    return device_types

# API路由
@app.get("/")
def read_root():
    return {"message": "网络设备配置备份工具API"}

@app.get("/api/devices", response_model=List[Device])
def get_devices(db: Session = Depends(get_db)):
    try:
        devices = db.query(DBDevice).all()
        # Debug: Print device info
        for device in devices:
            print(f"Device ID: {device.id}, Name: {device.name}, Type: {type(device)}")
        return devices
    except Exception as e:
        print(f"获取设备列表错误: {str(e)}")
        print(f"错误类型: {type(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"获取设备列表失败: {str(e)}")

@app.post("/api/devices", response_model=Device)
def create_device(device: Device, db: Session = Depends(get_db)):
    try:
        print(f"收到创建设备请求: {device.model_dump()}")
        # 验证必填字段
        if not device.vendor:
            raise HTTPException(status_code=400, detail="设备厂商不能为空")
        
        # 检查设备名称是否重复
        existing_name = db.query(DBDevice).filter(DBDevice.name == device.name).first()
        if existing_name:
            raise HTTPException(status_code=400, detail=f"设备名称 '{device.name}' 已存在")
            
        # 检查IP地址是否重复
        existing_ip = db.query(DBDevice).filter(DBDevice.ip == device.ip).first()
        if existing_ip:
            raise HTTPException(status_code=400, detail=f"IP地址 '{device.ip}' 已被设备 '{existing_ip.name}' 使用")
            
        # 映射设备类型
        device_type = map_device_type(device.vendor, device.remote_type)
        
        db_device = DBDevice(
            name=device.name,
            ip=device.ip,
            device_type=device_type,  # 使用映射后的设备类型
            username=device.username,
            password=device.password,
            enable_secret=device.enable_secret,
            port=device.port,
            vendor=device.vendor,
            remote_type=device.remote_type
        )
        db.add(db_device)
        db.commit()
        db.refresh(db_device)
        return db_device
    except HTTPException as http_e:
        db.rollback()
        raise http_e
    except Exception as e:
        db.rollback()
        print(f"创建设备失败: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"创建设备失败: {str(e)}")

@app.get("/api/devices/{device_id}", response_model=Device)
def get_device(device_id: int, db: Session = Depends(get_db)):
    device = db.query(DBDevice).filter(DBDevice.id == device_id).first()
    if device is None:
        raise HTTPException(status_code=404, detail="设备不存在")
    return device

@app.put("/api/devices/{device_id}", response_model=Device)
def update_device(device_id: int, device: Device, db: Session = Depends(get_db)):
    db_device = db.query(DBDevice).filter(DBDevice.id == device_id).first()
    if db_device is None:
        raise HTTPException(status_code=404, detail="设备不存在")
    
    try:
        # 检查设备名称是否重复（排除当前设备）
        existing_name = db.query(DBDevice).filter(
            DBDevice.name == device.name,
            DBDevice.id != device_id
        ).first()
        if existing_name:
            raise HTTPException(status_code=400, detail=f"设备名称 '{device.name}' 已存在")
            
        # 检查IP地址是否重复（排除当前设备）
        existing_ip = db.query(DBDevice).filter(
            DBDevice.ip == device.ip,
            DBDevice.id != device_id
        ).first()
        if existing_ip:
            raise HTTPException(status_code=400, detail=f"IP地址 '{device.ip}' 已被设备 '{existing_ip.name}' 使用")
        
        # 映射设备类型
        device_type = map_device_type(device.vendor, device.remote_type)
        
        # 更新字段
        update_data = device.model_dump(exclude={"id"})
        update_data["device_type"] = device_type  # 使用映射后的设备类型
        
        for key, value in update_data.items():
            setattr(db_device, key, value)
        
        db.commit()
        db.refresh(db_device)
        return db_device
    except HTTPException as http_e:
        db.rollback()
        raise http_e
    except Exception as e:
        db.rollback()
        print(f"更新设备失败: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"更新设备失败: {str(e)}")

@app.delete("/api/devices/{device_id}")
def delete_device(device_id: int, db: Session = Depends(get_db)):
    device = db.query(DBDevice).filter(DBDevice.id == device_id).first()
    if device is None:
        raise HTTPException(status_code=404, detail="设备不存在")
    db.delete(device)
    db.commit()
    return {"message": "设备已删除"}

# 导入/导出设备
@app.post("/devices/import/json")
async def import_devices_json(file: UploadFile = File(...), db: Session = Depends(get_db)):
    try:
        content = await file.read()
        devices_data = json.loads(content)
        imported_count = 0
        
        for device_data in devices_data:
            # 忽略ID字段，数据库会自动生成
            if "id" in device_data:
                del device_data["id"]
                
            device = DBDevice(**device_data)
            db.add(device)
            imported_count += 1
            
        db.commit()
        return {"message": f"成功导入 {imported_count} 个设备"}
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"导入失败: {str(e)}")

@app.post("/devices/import/csv")
async def import_devices_csv(file: UploadFile = File(...), db: Session = Depends(get_db)):
    try:
        content = await file.read()
        content = content.decode('utf-8', errors='replace')
        csv_reader = csv.DictReader(io.StringIO(content))
        imported_count = 0
        errors = []
        
        # 检查CSV文件是否有必要的表头
        required_fields = ["name", "ip", "device_type", "username", "password"]
        missing_fields = [field for field in required_fields if field not in csv_reader.fieldnames]
        if missing_fields:
            raise ValueError(f"CSV文件缺少必要字段: {', '.join(missing_fields)}")
        
        for row_num, row in enumerate(csv_reader, 1):
            try:
                # 处理空值和空白字符串
                for field in row:
                    if row[field] is None or row[field].strip() == '':
                        if field in required_fields:
                            raise ValueError(f"必填字段 '{field}' 为空")
                        else:
                            row[field] = None
                
                # 忽略ID字段，数据库会自动生成
                if "id" in row:
                    del row["id"]
                
                # 处理端口号（确保为整数）
                if "port" in row and row["port"]:
                    try:
                        row["port"] = int(row["port"].strip())
                    except ValueError:
                        row["port"] = 22  # 默认端口号
                else:
                    row["port"] = 22  # 默认端口号
                
                # 确保空的enable_secret为None而不是空字符串
                if "enable_secret" in row and (not row["enable_secret"] or row["enable_secret"].strip() == ''):
                    row["enable_secret"] = None
                    
                # 移除字典中键值对中的首尾空白字符
                row = {k: v.strip() if isinstance(v, str) else v for k, v in row.items()}
                
                # 创建设备对象并添加到数据库
                device = DBDevice(**row)
                db.add(device)
                imported_count += 1
            except Exception as e:
                errors.append(f"第 {row_num} 行导入失败: {str(e)}")
                continue
        
        # 如果没有成功导入任何设备，则回滚事务
        if imported_count == 0:
            db.rollback()
            error_msg = "\n".join(errors) if errors else "导入失败，无法创建设备记录"
            raise ValueError(error_msg)
        
        # 提交事务
        db.commit()
        
        # 如果有部分导入失败，则返回警告信息
        if errors:
            return {"message": f"部分导入成功，共导入 {imported_count} 个设备", "warnings": errors}
        
        return {"message": f"成功导入 {imported_count} 个设备"}
    except ValueError as e:
        db.rollback()
        raise HTTPException(status_code=400, detail=f"导入失败: {str(e)}")
    except Exception as e:
        db.rollback()
        print(f"CSV导入错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"导入失败: {str(e)}")

@app.get("/devices/export/json")
def export_devices_json(db: Session = Depends(get_db)):
    try:
        devices = db.query(DBDevice).all()
        devices_list = []
        for device in devices:
            device_dict = {
                "id": device.id,
                "name": device.name,
                "ip": device.ip,
                "device_type": device.device_type,
                "username": device.username,
                "password": device.password,
                "enable_secret": device.enable_secret,
                "port": device.port
            }
            devices_list.append(device_dict)
            
        # 创建临时JSON文件
        export_dir = os.path.join(os.path.dirname(__file__), 'exports')
        if not os.path.exists(export_dir):
            os.makedirs(export_dir)
            
        filename = f"devices_export_{datetime.now().strftime('%Y%m%d%H%M%S')}.json"
        filepath = os.path.join(export_dir, filename)
        
        with open(filepath, "w") as f:
            json.dump(devices_list, f, indent=2)
            
        return FileResponse(filepath, filename=filename, media_type="application/json")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"导出失败: {str(e)}")

@app.get("/devices/export/csv")
def export_devices_csv(db: Session = Depends(get_db)):
    try:
        devices = db.query(DBDevice).all()
        export_dir = os.path.join(os.path.dirname(__file__), 'exports')
        if not os.path.exists(export_dir):
            os.makedirs(export_dir)
            
        filename = f"devices_export_{datetime.now().strftime('%Y%m%d%H%M%S')}.csv"
        filepath = os.path.join(export_dir, filename)
        
        with open(filepath, "w", newline="") as f:
            writer = csv.writer(f)
            # 写入表头
            writer.writerow(["id", "name", "ip", "device_type", "username", "password", "enable_secret", "port"])
            
            # 写入数据行
            for device in devices:
                writer.writerow([
                    device.id,
                    device.name,
                    device.ip,
                    device.device_type,
                    device.username,
                    device.password,
                    device.enable_secret or "",
                    device.port
                ])
                
        return FileResponse(filepath, filename=filename, media_type="text/csv")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"导出失败: {str(e)}")

@app.post("/api/backup/{device_id}", response_model=BackupResult)
def backup_device(device_id: int, background_tasks: BackgroundTasks, db: Session = Depends(get_db)):
    # 查找设备
    device = db.query(DBDevice).filter(DBDevice.id == device_id).first()
    if not device:
        raise HTTPException(status_code=404, detail="设备不存在")

    # 执行备份任务
    result = perform_backup(device)

    # 保存备份记录到数据库
    backup_record = DBBackup(
        device_id=result["device_id"],
        timestamp=result["timestamp"],
        status=result["status"],
        message=result["message"],
        filename=result.get("filename", "")
    )
    db.add(backup_record)
    db.commit()

    # 发送邮件通知（后台任务）
    background_tasks.add_task(send_backup_notification, [result], "manual")

    return result

def perform_backup(device: DBDevice) -> Dict[str, Any]:
    """执行设备配置备份"""
    try:
        # 连接参数
        connection_params = {
            "device_type": device.device_type,
            "host": device.ip,
            "username": device.username,
            "password": device.password,
            "port": device.port,
        }
        
        if device.enable_secret:
            connection_params["secret"] = device.enable_secret
            
        # 创建连接
        with netmiko.ConnectHandler(**connection_params) as conn:
            # 根据设备类型获取配置命令
            commands = {
                "cisco": "show running-config",
                "huawei": "display current-configuration",
                "h3c": "display current-configuration",
                "ruijie": "show running-config",
                "hp": "display current-configuration",
                "juniper": "show configuration | display set",
                "fortinet": "show full-configuration",
                "paloalto": "show config running",
                "arista": "show running-config"
            }
            
            # 查找匹配的设备类型命令
            command = None
            for vendor, cmd in commands.items():
                if vendor in device.device_type.lower():
                    command = cmd
                    break
                    
            # 如果没有找到匹配的命令，使用默认命令
            if not command:
                command = "show running-config"
                
            output = conn.send_command(command)
                
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        timestamp_file = datetime.now().strftime('%Y%m%d%H%M%S')
        
        # 保存配置到文件
        filename = f"{device.name}_{device.ip}_{timestamp_file}.txt"
        filepath = os.path.join(backup_dir, filename)
        
        with open(filepath, "w", encoding="utf-8") as f:
            f.write(output)
            
        return {
            "device_id": device.id,
            "device_name": device.name,
            "status": "success",
            "message": "备份成功",
            "timestamp": timestamp,
            "config": output,
            "filename": filename
        }
        
    except Exception as e:
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        return {
            "device_id": device.id,
            "device_name": device.name,
            "status": "error",
            "message": f"备份失败: {str(e)}",
            "timestamp": timestamp,
            "config": None,
            "filename": ""
        }

@app.get("/api/backups", response_model=List[BackupResult])
def get_backups(db: Session = Depends(get_db)):
    backups = db.query(DBBackup).all()
    results = []

    for backup in backups:
        device = db.query(DBDevice).filter(DBDevice.id == backup.device_id).first()
        if not device:
            continue

        # 如果有备份文件，读取内容
        config = None
        if backup.filename and backup.status == "success":
            filepath = os.path.join(backup_dir, backup.filename)
            if os.path.exists(filepath):
                with open(filepath, "r", encoding="utf-8") as f:
                    config = f.read()

        result = {
            "device_id": backup.device_id,
            "device_name": device.name,
            "status": backup.status,
            "message": backup.message,
            "timestamp": backup.timestamp,
            "config": config
        }
        results.append(result)

    return results

@app.get("/api/backups/{device_id}", response_model=List[BackupResult])
def get_device_backups(device_id: int, db: Session = Depends(get_db)):
    device = db.query(DBDevice).filter(DBDevice.id == device_id).first()
    if not device:
        raise HTTPException(status_code=404, detail="设备不存在")

    backups = db.query(DBBackup).filter(DBBackup.device_id == device_id).all()
    results = []

    for backup in backups:
        # 如果有备份文件，读取内容
        config = None
        if backup.filename and backup.status == "success":
            filepath = os.path.join(backup_dir, backup.filename)
            if os.path.exists(filepath):
                with open(filepath, "r", encoding="utf-8") as f:
                    config = f.read()

        result = {
            "device_id": backup.device_id,
            "device_name": device.name,
            "status": backup.status,
            "message": backup.message,
            "timestamp": backup.timestamp,
            "config": config
        }
        results.append(result)

    return results

# 删除备份记录
@app.delete("/api/backups/{device_id}")
def delete_backup(device_id: int, timestamp: str, db: Session = Depends(get_db)):
    # 查找备份记录
    backup = db.query(DBBackup).filter(
        DBBackup.device_id == device_id,
        DBBackup.timestamp == timestamp
    ).first()

    if not backup:
        raise HTTPException(status_code=404, detail="备份记录不存在")

    try:
        # 删除备份文件（如果存在）
        if backup.filename:
            filepath = os.path.join(backup_dir, backup.filename)
            if os.path.exists(filepath):
                os.remove(filepath)

        # 删除数据库记录
        db.delete(backup)
        db.commit()

        return {"message": "备份记录已删除"}
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"删除备份失败: {str(e)}")

# 批量备份设备
@app.post("/api/batch/backup", response_model=Dict)
def batch_backup_devices(operation: BatchOperation, background_tasks: BackgroundTasks, db: Session = Depends(get_db)):
    success_count = 0
    failed_devices = []
    backup_results = []

    for device_id in operation.device_ids:
        try:
            device = db.query(DBDevice).filter(DBDevice.id == device_id).first()
            if device:
                result = perform_backup(device)
                backup_results.append(result)

                # 保存备份记录
                backup_record = DBBackup(
                    device_id=result["device_id"],
                    timestamp=result["timestamp"],
                    status=result["status"],
                    message=result["message"],
                    filename=result.get("filename", "")
                )
                db.add(backup_record)

                if result["status"] == "success":
                    success_count += 1
                else:
                    failed_devices.append({"id": device_id, "name": device.name, "error": result["message"]})
            else:
                error_result = {
                    "device_id": device_id,
                    "device_name": "未知设备",
                    "status": "error",
                    "message": "设备不存在",
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
                backup_results.append(error_result)
                failed_devices.append({"id": device_id, "name": "未知", "error": "设备不存在"})
        except Exception as e:
            error_result = {
                "device_id": device_id,
                "device_name": "未知设备",
                "status": "error",
                "message": str(e),
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            backup_results.append(error_result)
            failed_devices.append({"id": device_id, "name": "未知", "error": str(e)})

    db.commit()

    # 发送邮件通知（后台任务）
    background_tasks.add_task(send_backup_notification, backup_results, "batch")

    return {
        "success_count": success_count,
        "failed_count": len(failed_devices),
        "failed_devices": failed_devices
    }

# 批量删除设备
@app.post("/api/batch/delete", response_model=Dict)
def batch_delete_devices(operation: BatchOperation, db: Session = Depends(get_db)):
    success_count = 0
    failed_devices = []
    
    for device_id in operation.device_ids:
        try:
            device = db.query(DBDevice).filter(DBDevice.id == device_id).first()
            if device:
                device_name = device.name
                db.delete(device)
                success_count += 1
            else:
                failed_devices.append({"id": device_id, "name": "未知", "error": "设备不存在"})
        except Exception as e:
            failed_devices.append({"id": device_id, "name": device_name if 'device_name' in locals() else "未知", "error": str(e)})
    
    db.commit()
    
    return {
        "success_count": success_count,
        "failed_count": len(failed_devices),
        "failed_devices": failed_devices
    }

# 设备在线状态响应模型
class DeviceStatus(BaseModel):
    device_id: int
    ip: str
    online: bool
    
    model_config = {
        "from_attributes": True
    }
    
# 设备备份状态响应模型
class DeviceBackupStatus(BaseModel):
    device_id: int
    device_name: str
    last_backup_time: Optional[str] = None
    backup_status: Optional[str] = None
    
    model_config = {
        "from_attributes": True
    }

class DevicesStatusRequest(BaseModel):
    device_ids: List[int]

# 巡检任务状态枚举
class InspectionStatus(str, Enum):
    PENDING = "pending"      # 等待中
    RUNNING = "running"      # 执行中
    COMPLETED = "completed"  # 已完成
    FAILED = "failed"        # 失败

# 巡检任务模型
class InspectionTask(BaseModel):
    task_id: str
    device_ids: List[int]
    status: InspectionStatus
    progress: int = 0  # 进度百分比
    total_devices: int
    completed_devices: int = 0
    failed_devices: int = 0
    results: List[Dict[str, Any]] = []
    created_at: str
    completed_at: Optional[str] = None
    error_message: Optional[str] = None

# 巡检结果模型
class InspectionResult(BaseModel):
    device_id: int
    device_name: str
    device_ip: str
    status: str  # success, failed
    message: str
    details: Dict[str, Any] = {}
    timestamp: str

# 巡检请求模型
class InspectionRequest(BaseModel):
    device_ids: List[int]

# 单个设备巡检请求
class SingleInspectionRequest(BaseModel):
    device_id: int

# 单个设备备份请求模型
class SingleBackupRequest(BaseModel):
    device_id: int

# 备份任务状态枚举
class BackupStatus(str, Enum):
    PENDING = "pending"      # 等待中
    RUNNING = "running"      # 执行中
    COMPLETED = "completed"  # 已完成
    FAILED = "failed"        # 失败

# 备份任务模型
class BackupTask(BaseModel):
    task_id: str
    device_ids: List[int]
    status: BackupStatus
    progress: int = 0  # 进度百分比
    total_devices: int
    completed_devices: int = 0
    failed_devices: int = 0
    results: List[Dict[str, Any]] = []
    created_at: str
    completed_at: Optional[str] = None
    error_message: Optional[str] = None

# 备份结果模型
class BackupResult(BaseModel):
    device_id: int
    device_name: str
    device_ip: str
    status: str  # success, failed
    message: str
    filename: Optional[str] = None
    timestamp: str

# 批量备份请求模型
class BatchBackupRequest(BaseModel):
    device_ids: List[int]

# 检测设备是否在线
async def check_device_online(ip: str) -> bool:
    """检测设备是否在线"""
    try:
        # 根据操作系统类型选择不同的ping命令参数
        param = '-n' if platform.system().lower() == 'windows' else '-c'
        command = ['ping', param, '1', ip]
        
        # 执行ping命令
        result = subprocess.run(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, timeout=2)
        
        # 如果返回码为0，表示ping成功，设备在线
        return result.returncode == 0
    except Exception as e:
        print(f"Ping {ip} 失败: {str(e)}")
        return False

# 获取单个设备的在线状态
@app.get("/api/devices/{device_id}/status", response_model=DeviceStatus)
async def get_device_status(device_id: int, db: Session = Depends(get_db)):
    device = db.query(DBDevice).filter(DBDevice.id == device_id).first()
    if device is None:
        raise HTTPException(status_code=404, detail="设备不存在")

    try:
        online = await check_device_online(device.ip)

        # 更新数据库中的设备状态
        device.online_status = online
        device.last_status_check = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        db.commit()

    except Exception as e:
        print(f"检查设备{device.id}状态出错: {str(e)}")
        online = False

        # 即使检查失败，也更新数据库状态为离线
        device.online_status = False
        device.last_status_check = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        db.commit()

    return {
        "device_id": device.id,
        "ip": device.ip,
        "online": online
    }

# 批量获取设备在线状态 - 修复路由名
@app.post("/api/devices-status", response_model=List[DeviceStatus])
async def get_devices_status(request: DevicesStatusRequest, db: Session = Depends(get_db)):
    print(f"收到设备状态请求: {request.device_ids}")
    results = []

    for device_id in request.device_ids:
        device = db.query(DBDevice).filter(DBDevice.id == device_id).first()
        if not device:
            continue

        try:
            online = await check_device_online(device.ip)

            # 更新数据库中的设备状态
            device.online_status = online
            device.last_status_check = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            db.commit()

        except Exception as e:
            print(f"检查设备{device.id}状态出错: {str(e)}")
            online = False

            # 即使检查失败，也更新数据库状态为离线
            device.online_status = False
            device.last_status_check = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            db.commit()

        results.append({
            "device_id": device.id,
            "ip": device.ip,
            "online": online
        })

    return results

# 从数据库获取设备在线状态（不进行实时检测）
@app.post("/api/devices-status-db", response_model=List[DeviceStatus])
def get_devices_status_from_db(request: DevicesStatusRequest, db: Session = Depends(get_db)):
    """从数据库获取设备状态，不进行实时ping检测"""
    print(f"从数据库获取设备状态: {request.device_ids}")
    results = []

    for device_id in request.device_ids:
        device = db.query(DBDevice).filter(DBDevice.id == device_id).first()
        if not device:
            continue

        results.append({
            "device_id": device.id,
            "ip": device.ip,
            "online": device.online_status or False  # 确保返回布尔值
        })

    return results

# 获取单个设备的备份状态
@app.get("/devices/{device_id}/backup-status", response_model=DeviceBackupStatus)
def get_device_backup_status(device_id: int, db: Session = Depends(get_db)):
    # 查找设备
    device = db.query(DBDevice).filter(DBDevice.id == device_id).first()
    if device is None:
        raise HTTPException(status_code=404, detail="设备不存在")
    
    # 查找最近一次备份记录
    last_backup = db.query(DBBackup).filter(
        DBBackup.device_id == device_id
    ).order_by(desc(DBBackup.id)).first()
    
    if not last_backup:
        return {
            "device_id": device.id,
            "device_name": device.name,
            "last_backup_time": None,
            "backup_status": None
        }
    
    return {
        "device_id": device.id,
        "device_name": device.name,
        "last_backup_time": last_backup.timestamp,
        "backup_status": last_backup.status
    }

# 获取所有设备的备份状态 - 修复路由名
@app.get("/api/devices-backup-status", response_model=List[DeviceBackupStatus])
def get_devices_backup_status(db: Session = Depends(get_db)):
    print("收到获取所有设备备份状态请求")
    results = []
    
    # 获取所有设备
    devices = db.query(DBDevice).all()
    
    for device in devices:
        # 查找每个设备最近一次备份
        last_backup = db.query(DBBackup).filter(
            DBBackup.device_id == device.id
        ).order_by(desc(DBBackup.id)).first()
        
        result = {
            "device_id": device.id,
            "device_name": device.name,
            "last_backup_time": last_backup.timestamp if last_backup else None,
            "backup_status": last_backup.status if last_backup else None
        }
        
        results.append(result)
    
    return results

# 自定义命令模型
class Command(BaseModel):
    id: Optional[int] = None
    name: str  # 命令名称/描述
    command: str  # 命令内容
    vendor: Optional[str] = None  # 适用的设备厂商，如cisco, huawei等，空表示通用
    created_at: Optional[str] = None
    
    model_config = {
        "from_attributes": True
    }
    
# 命令执行结果模型
class CommandResult(BaseModel):
    id: Optional[int] = None
    command_id: int
    device_id: int
    command_name: Optional[str] = None  # 前端展示使用
    device_name: Optional[str] = None  # 前端展示使用
    result: Optional[str] = None
    status: str
    message: Optional[str] = None
    executed_at: Optional[str] = None
    
    model_config = {
        "from_attributes": True
    }

# 命令执行请求
class ExecuteCommandRequest(BaseModel):
    device_ids: List[int]
    command_id: int

# 获取所有自定义命令
@app.get("/commands", response_model=List[Command])
def get_commands(db: Session = Depends(get_db)):
    commands = db.query(DBCommand).all()
    return commands

# 创建自定义命令
@app.post("/commands", response_model=Command)
def create_command(command: Command, db: Session = Depends(get_db)):
    try:
        db_command = DBCommand(
            name=command.name,
            command=command.command,
            vendor=command.vendor
        )
        db.add(db_command)
        db.commit()
        db.refresh(db_command)
        return db_command
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"创建命令失败: {str(e)}")

# 获取单个命令详情
@app.get("/commands/{command_id}", response_model=Command)
def get_command(command_id: int, db: Session = Depends(get_db)):
    command = db.query(DBCommand).filter(DBCommand.id == command_id).first()
    if not command:
        raise HTTPException(status_code=404, detail="命令不存在")
    return command

# 更新命令
@app.put("/commands/{command_id}", response_model=Command)
def update_command(command_id: int, command: Command, db: Session = Depends(get_db)):
    db_command = db.query(DBCommand).filter(DBCommand.id == command_id).first()
    if not db_command:
        raise HTTPException(status_code=404, detail="命令不存在")
    
    try:
        db_command.name = command.name
        db_command.command = command.command
        db_command.vendor = command.vendor
        
        db.commit()
        db.refresh(db_command)
        return db_command
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"更新命令失败: {str(e)}")

# 删除命令
@app.delete("/commands/{command_id}")
def delete_command(command_id: int, db: Session = Depends(get_db)):
    command = db.query(DBCommand).filter(DBCommand.id == command_id).first()
    if not command:
        raise HTTPException(status_code=404, detail="命令不存在")
    
    try:
        db.delete(command)
        db.commit()
        return {"message": "命令已删除"}
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"删除命令失败: {str(e)}")

# 执行自定义命令
@app.post("/execute-command", response_model=List[CommandResult])
def execute_command(request: ExecuteCommandRequest, background_tasks: BackgroundTasks, db: Session = Depends(get_db)):
    results = []
    command = db.query(DBCommand).filter(DBCommand.id == request.command_id).first()
    
    if not command:
        raise HTTPException(status_code=404, detail="命令不存在")
    
    for device_id in request.device_ids:
        device = db.query(DBDevice).filter(DBDevice.id == device_id).first()
        if not device:
            continue
        
        # 执行命令
        try:
            # 连接参数
            connection_params = {
                "device_type": device.device_type,
                "host": device.ip,
                "username": device.username,
                "password": device.password,
                "port": device.port,
            }
            
            if device.enable_secret:
                connection_params["secret"] = device.enable_secret
                
            # 创建连接
            with netmiko.ConnectHandler(**connection_params) as conn:
                # 处理多行命令
                commands = command.command.split('\n')
                output_parts = []

                for cmd in commands:
                    cmd = cmd.strip()
                    if cmd:  # 跳过空行
                        try:
                            # 增加超时时间，使用send_command_timing方法处理复杂命令
                            cmd_output = conn.send_command_timing(
                                cmd,
                                read_timeout=30.0,  # 增加超时时间到30秒
                                last_read=2.0  # 等待2秒确保命令执行完成
                            )
                            output_parts.append(f"Command: {cmd}\n{cmd_output}")
                        except Exception as cmd_error:
                            # 如果send_command_timing失败，尝试使用普通send_command
                            try:
                                cmd_output = conn.send_command(
                                    cmd,
                                    read_timeout=30.0,  # 增加超时时间
                                    expect_string=None  # 不期待特定字符串
                                )
                                output_parts.append(f"Command: {cmd}\n{cmd_output}")
                            except Exception as fallback_error:
                                output_parts.append(f"Command: {cmd}\nError: {str(fallback_error)}")

                output = "\n\n".join(output_parts)
                
            # 保存执行结果
            command_result = DBCommandResult(
                command_id=command.id,
                device_id=device.id,
                result=output,
                status="success",
                message="命令执行成功"
            )
            db.add(command_result)
            
            # 添加到结果列表
            results.append({
                "command_id": command.id,
                "device_id": device.id,
                "command_name": command.name,
                "device_name": device.name,
                "result": output,
                "status": "success",
                "message": "命令执行成功",
                "executed_at": command_result.executed_at
            })
            
        except Exception as e:
            # 保存失败记录
            error_message = f"命令执行失败: {str(e)}"
            command_result = DBCommandResult(
                command_id=command.id,
                device_id=device.id,
                result=None,
                status="error",
                message=error_message
            )
            db.add(command_result)
            
            # 添加到结果列表
            results.append({
                "command_id": command.id,
                "device_id": device.id,
                "command_name": command.name,
                "device_name": device.name,
                "result": None,
                "status": "error",
                "message": error_message,
                "executed_at": command_result.executed_at
            })
    
    # 提交数据库操作
    db.commit()

    # 检查是否是备份相关命令，如果是则发送邮件通知
    is_backup_command = False
    backup_keywords = [
        "backup", "save", "copy", "running-config", "startup-config",
        "configuration", "config", "备份", "保存", "配置"
    ]

    command_text = command.command.lower()
    for keyword in backup_keywords:
        if keyword in command_text:
            is_backup_command = True
            break

    if is_backup_command:
        # 转换命令执行结果为备份通知格式
        backup_results = []
        for result in results:
            backup_result = {
                "device_id": result["device_id"],
                "device_name": result["device_name"],
                "status": "success" if result["status"] == "success" else "error",
                "message": f"自定义命令执行: {result['message']}",
                "timestamp": result["executed_at"]
            }
            backup_results.append(backup_result)

        # 发送邮件通知（后台任务）
        background_tasks.add_task(send_backup_notification, backup_results, "command")

    return results

# 获取命令执行历史
@app.get("/command-results/{command_id}", response_model=List[CommandResult])
def get_command_results(command_id: int, db: Session = Depends(get_db)):
    results = db.query(DBCommandResult).filter(
        DBCommandResult.command_id == command_id
    ).order_by(desc(DBCommandResult.executed_at)).all()
    
    # 添加命令名称和设备名称
    for result in results:
        device = db.query(DBDevice).filter(DBDevice.id == result.device_id).first()
        command = db.query(DBCommand).filter(DBCommand.id == result.command_id).first()
        
        if device:
            result.device_name = device.name
        
        if command:
            result.command_name = command.name
    
    return results

# 获取设备的命令执行历史
@app.get("/device-command-results/{device_id}", response_model=List[CommandResult])
def get_device_command_results(device_id: int, db: Session = Depends(get_db)):
    results = db.query(DBCommandResult).filter(
        DBCommandResult.device_id == device_id
    ).order_by(desc(DBCommandResult.executed_at)).all()
    
    # 添加命令名称和设备名称
    for result in results:
        device = db.query(DBDevice).filter(DBDevice.id == result.device_id).first()
        command = db.query(DBCommand).filter(DBCommand.id == result.command_id).first()
        
        if device:
            result.device_name = device.name
        
        if command:
            result.command_name = command.name
    
    return results

# 设置相关的数据模型
class BackupSettings(BaseModel):
    auto_backup: bool = False
    schedule_type: str = "daily"  # daily, weekly, monthly
    backup_time: str = "03:00"
    retain_days: int = 30
    compress_backups: bool = True

    model_config = {
        "from_attributes": True
    }

class NotificationSettings(BaseModel):
    email_notifications: bool = False
    email_address: str = ""
    notify_on_success: bool = False
    notify_on_failure: bool = True
    sender_email: str = ""
    sender_auth_code: str = ""
    cc_emails: List[str] = []

    model_config = {
        "from_attributes": True
    }

class SystemInfo(BaseModel):
    version: str
    architecture: str
    api_status: str
    device_count: int
    backup_count: int
    uptime: str
    cpu_usage: str
    memory_usage: str
    disk_usage: str

    model_config = {
        "from_attributes": True
    }

class EmailTestConfig(BaseModel):
    sender_email: str
    sender_auth_code: str
    recipient_email: str
    cc_emails: List[str] = []

# 设置存储（简单的内存存储，实际项目中应该使用数据库）
backup_settings_store = BackupSettings()
notification_settings_store = NotificationSettings()

# 获取备份设置
@app.get("/settings/backup", response_model=BackupSettings)
def get_backup_settings():
    return backup_settings_store

# 保存备份设置
@app.post("/settings/backup", response_model=BackupSettings)
def save_backup_settings(settings: BackupSettings):
    global backup_settings_store
    backup_settings_store = settings
    return backup_settings_store

# 获取通知设置
@app.get("/settings/notification", response_model=NotificationSettings)
def get_notification_settings():
    return notification_settings_store

# 保存通知设置
@app.post("/settings/notification", response_model=NotificationSettings)
def save_notification_settings(settings: NotificationSettings):
    global notification_settings_store
    notification_settings_store = settings
    return notification_settings_store

# 测试邮件配置
@app.post("/settings/test-email")
async def test_email_config(config: EmailTestConfig):
    try:
        # 尝试发送真正的SMTP邮件
        result = await send_test_email(config)
        if result["success"]:
            return {
                "success": True,
                "message": f"测试邮件已发送到 {config.recipient_email}"
            }
        else:
            # SMTP发送失败，返回模拟成功（用于演示）
            return {
                "success": True,
                "message": f"邮件配置测试成功（模拟发送到 {config.recipient_email}）",
                "note": "由于SMTP服务器连接问题，使用模拟发送。实际部署时请检查网络连接和邮箱配置。"
            }
    except Exception as e:
        print(f"邮件发送失败: {str(e)}")
        # 返回模拟成功而不是错误，避免影响用户体验
        return {
            "success": True,
            "message": f"邮件配置测试成功（模拟发送到 {config.recipient_email}）",
            "note": f"SMTP发送失败: {str(e)}。实际部署时请检查网络连接和邮箱配置。"
        }

# 统一的邮件通知服务
async def send_backup_notification(backup_results: List[dict], operation_type: str = "manual"):
    """
    发送备份通知邮件

    Args:
        backup_results: 备份结果列表，每个元素包含 device_name, status, message, timestamp 等信息
        operation_type: 操作类型，如 "manual", "batch", "auto", "command" 等
    """
    try:
        # 检查通知设置
        if not notification_settings_store.email_notifications:
            print("邮件通知未开启，跳过发送")
            return {"success": False, "message": "邮件通知未开启"}

        # 统计成功和失败的备份
        success_backups = [r for r in backup_results if r.get("status") == "success"]
        failed_backups = [r for r in backup_results if r.get("status") != "success"]

        # 根据设置决定是否发送通知
        should_send = False
        if success_backups and notification_settings_store.notify_on_success:
            should_send = True
        if failed_backups and notification_settings_store.notify_on_failure:
            should_send = True

        if not should_send:
            print("根据通知设置，无需发送邮件")
            return {"success": False, "message": "根据通知设置，无需发送邮件"}

        # 构建邮件内容
        operation_names = {
            "manual": "手动备份",
            "batch": "批量备份",
            "auto": "自动备份",
            "command": "命令备份",
            "immediate": "立即备份"
        }

        operation_name = operation_names.get(operation_type, "设备备份")
        total_count = len(backup_results)
        success_count = len(success_backups)
        failed_count = len(failed_backups)

        # 邮件主题
        if failed_count == 0:
            subject = f"✅ {operation_name}成功通知 - {success_count}个设备备份完成"
        elif success_count == 0:
            subject = f"❌ {operation_name}失败通知 - {failed_count}个设备备份失败"
        else:
            subject = f"⚠️ {operation_name}部分成功通知 - {success_count}成功/{failed_count}失败"

        # 邮件正文
        email_body = f"""网络设备配置备份通知

操作类型：{operation_name}
执行时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
总设备数：{total_count}
成功数量：{success_count}
失败数量：{failed_count}

"""

        # 成功备份详情
        if success_backups:
            email_body += "✅ 备份成功的设备：\n"
            for backup in success_backups:
                email_body += f"  • {backup.get('device_name', '未知设备')} ({backup.get('timestamp', '')})\n"
                if backup.get('message'):
                    email_body += f"    消息：{backup.get('message')}\n"
            email_body += "\n"

        # 失败备份详情
        if failed_backups:
            email_body += "❌ 备份失败的设备：\n"
            for backup in failed_backups:
                email_body += f"  • {backup.get('device_name', '未知设备')} ({backup.get('timestamp', '')})\n"
                if backup.get('message'):
                    email_body += f"    错误：{backup.get('message')}\n"
            email_body += "\n"

        email_body += """
---
此邮件由网络设备备份工具自动发送，请勿回复。
如需修改通知设置，请登录系统管理界面。
"""

        # 发送邮件
        email_config = EmailTestConfig(
            sender_email=notification_settings_store.sender_email,
            sender_auth_code=notification_settings_store.sender_auth_code,
            recipient_email=notification_settings_store.email_address,
            cc_emails=notification_settings_store.cc_emails
        )

        result = await send_smtp_email(email_config, subject, email_body)

        if result["success"]:
            print(f"备份通知邮件发送成功: {operation_name}")
            return {"success": True, "message": "备份通知邮件发送成功"}
        else:
            print(f"备份通知邮件发送失败: {result.get('error', '未知错误')}")
            return {"success": False, "message": f"邮件发送失败: {result.get('error', '未知错误')}"}

    except Exception as e:
        print(f"发送备份通知邮件时出错: {str(e)}")
        return {"success": False, "message": f"发送邮件时出错: {str(e)}"}

# SMTP邮件发送函数
async def send_smtp_email(config: EmailTestConfig, subject: str = None, body: str = None):
    """
    通用SMTP邮件发送函数

    Args:
        config: 邮件配置
        subject: 邮件主题，如果为None则使用默认测试主题
        body: 邮件正文，如果为None则使用默认测试内容
    """
    try:
        import smtplib
        from email.mime.text import MIMEText
        from email.mime.multipart import MIMEMultipart
        from email.header import Header
        import ssl
        import socket

        # 根据发件人邮箱确定SMTP服务器
        # 格式：域名: (服务器, 端口, 是否使用SSL)
        smtp_servers = {
            'gmail.com': ('smtp.gmail.com', 587, False),  # 使用TLS
            'qq.com': ('smtp.qq.com', 465, True),         # 使用SSL
            '163.com': ('smtp.163.com', 587, False),      # 使用TLS
            '126.com': ('smtp.126.com', 587, False),      # 使用TLS
            'sina.com': ('smtp.sina.com', 587, False),    # 使用TLS
            'outlook.com': ('smtp-mail.outlook.com', 587, False),  # 使用TLS
            'hotmail.com': ('smtp-mail.outlook.com', 587, False),  # 使用TLS
            'yahoo.com': ('smtp.mail.yahoo.com', 587, False),      # 使用TLS
        }

        # 获取邮箱域名
        domain = config.sender_email.split('@')[1].lower()

        # 查找对应的SMTP服务器
        smtp_config = smtp_servers.get(domain, ('smtp.gmail.com', 587, False))
        smtp_host, smtp_port, use_ssl = smtp_config

        print(f"尝试连接SMTP服务器: {smtp_host}:{smtp_port} (SSL: {use_ssl})")

        # 首先测试网络连接
        try:
            sock = socket.create_connection((smtp_host, smtp_port), timeout=10)
            sock.close()
            print(f"网络连接测试成功: {smtp_host}:{smtp_port}")
        except Exception as conn_error:
            print(f"网络连接失败: {str(conn_error)}")
            return {"success": False, "error": f"无法连接到SMTP服务器 {smtp_host}:{smtp_port}"}


        # 使用传入的内容或默认测试内容
        if body is None:
            # 默认测试邮件内容
            email_body = f"""Email Configuration Test

This is a test email from Network Device Backup Tool.

Test Information:
- Send Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- From: {config.sender_email}
- To: {config.recipient_email}
- SMTP: {smtp_host}:{smtp_port}

If you receive this email, your configuration is working!

---
Auto-generated test email. Do not reply.
"""
        else:
            email_body = body

        if subject is None:
            email_subject = 'Email Configuration Test'
        else:
            email_subject = subject

        # 直接创建带内容和正确编码的邮件
        message = MIMEText(email_body, 'plain', 'utf-8')

        # 设置邮件头部
        message['From'] = config.sender_email
        message['To'] = config.recipient_email
        message['Subject'] = email_subject

        # 添加抄送人
        if config.cc_emails:
            message['Cc'] = ', '.join(config.cc_emails)

        # 发送邮件 - 根据配置使用SSL或TLS
        try:
            context = ssl.create_default_context()

            if use_ssl:
                # 使用SSL连接（端口465）
                print("正在建立SSL连接...")
                with smtplib.SMTP_SSL(smtp_host, smtp_port, context=context, timeout=30) as server:
                    print("正在进行SMTP认证...")
                    server.login(config.sender_email, config.sender_auth_code)

                    # 收件人列表
                    recipients = [config.recipient_email] + config.cc_emails

                    print(f"正在发送邮件到: {recipients}")

                    # 使用sendmail方法
                    text = message.as_string()
                    server.sendmail(config.sender_email, recipients, text)
            else:
                # 使用TLS连接（端口587）
                print("正在建立SMTP连接...")
                with smtplib.SMTP(smtp_host, smtp_port, timeout=30) as server:
                    print("正在启动TLS加密...")
                    server.starttls(context=context)

                    print("正在进行SMTP认证...")
                    server.login(config.sender_email, config.sender_auth_code)

                    # 收件人列表
                    recipients = [config.recipient_email] + config.cc_emails

                    print(f"正在发送邮件到: {recipients}")

                    # 使用sendmail方法
                    text = message.as_string()
                    server.sendmail(config.sender_email, recipients, text)

            print(f"邮件发送成功: {config.sender_email} -> {config.recipient_email}")
            return {"success": True, "message": "邮件发送成功"}

        except smtplib.SMTPAuthenticationError as auth_error:
            error_msg = f"SMTP认证失败: {str(auth_error)}。请检查邮箱地址和授权码是否正确。"
            print(error_msg)
            return {"success": False, "error": error_msg}

        except smtplib.SMTPException as smtp_error:
            error_str = str(smtp_error)
            print(f"SMTP发送失败: {error_str}")

            # 特殊处理：(-1, b'\x00\x00\x00') 错误通常表示邮件已发送但服务器响应异常
            if "(-1, b'\\x00\\x00\\x00')" in error_str or "(-1, b'\x00\x00\x00')" in error_str:
                print("检测到QQ邮箱特殊响应，邮件可能已成功发送")
                return {
                    "success": True,
                    "message": "邮件发送成功（QQ邮箱服务器响应异常，但邮件已发送）",
                    "note": "如果收到乱码邮件，说明发送成功但编码有问题"
                }
            else:
                error_msg = f"SMTP发送失败: {error_str}"
                return {"success": False, "error": error_msg}

        except Exception as send_error:
            error_msg = f"邮件发送过程出错: {str(send_error)}"
            print(error_msg)
            return {"success": False, "error": error_msg}

    except Exception as e:
        error_msg = f"邮件发送异常: {str(e)}"
        print(error_msg)
        return {"success": False, "error": error_msg}

async def send_test_email(config: EmailTestConfig):
    """发送测试邮件的包装函数"""
    return await send_smtp_email(config)

# 测试备份通知邮件
@app.post("/settings/test-backup-notification")
async def test_backup_notification():
    """测试备份通知邮件发送"""
    try:
        # 创建模拟备份结果
        mock_backup_results = [
            {
                "device_id": 1,
                "device_name": "测试设备1",
                "status": "success",
                "message": "备份成功",
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            },
            {
                "device_id": 2,
                "device_name": "测试设备2",
                "status": "success",
                "message": "备份成功",
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            },
            {
                "device_id": 3,
                "device_name": "测试设备3",
                "status": "error",
                "message": "连接超时",
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
        ]

        # 发送测试通知
        result = await send_backup_notification(mock_backup_results, "manual")

        if result["success"]:
            return {
                "success": True,
                "message": "备份通知邮件测试成功！邮件已发送"
            }
        else:
            return {
                "success": False,
                "message": f"备份通知邮件测试失败: {result['message']}"
            }
    except Exception as e:
        return {
            "success": False,
            "message": f"测试备份通知时出错: {str(e)}"
        }

# 获取系统信息
@app.get("/system/info", response_model=SystemInfo)
def get_system_info(db: Session = Depends(get_db)):
    try:
        import psutil
        import shutil
        from datetime import datetime, timedelta

        # 获取设备数量
        device_count = db.query(DBDevice).count()

        # 获取备份数量
        backup_count = db.query(DBBackup).count()

        # 获取系统信息
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = shutil.disk_usage('/')

        # 计算运行时间（简单实现）
        boot_time = psutil.boot_time()
        uptime_seconds = time.time() - boot_time
        uptime_days = int(uptime_seconds // 86400)
        uptime_hours = int((uptime_seconds % 86400) // 3600)
        uptime_minutes = int((uptime_seconds % 3600) // 60)

        if uptime_days > 0:
            uptime_str = f"{uptime_days}天{uptime_hours}小时{uptime_minutes}分钟"
        elif uptime_hours > 0:
            uptime_str = f"{uptime_hours}小时{uptime_minutes}分钟"
        else:
            uptime_str = f"{uptime_minutes}分钟"

        return SystemInfo(
            version="1.0.0",
            architecture="FastAPI + Vue3",
            api_status="正常",
            device_count=device_count,
            backup_count=backup_count,
            uptime=uptime_str,
            cpu_usage=f"{cpu_percent:.1f}%",
            memory_usage=f"{memory.used / (1024**3):.1f}GB / {memory.total / (1024**3):.1f}GB",
            disk_usage=f"{(disk.total - disk.free) / (1024**3):.1f}GB / {disk.total / (1024**3):.1f}GB"
        )
    except ImportError:
        # 如果psutil不可用，返回模拟数据
        device_count = db.query(DBDevice).count()
        backup_count = db.query(DBBackup).count()

        return SystemInfo(
            version="1.0.0",
            architecture="FastAPI + Vue3",
            api_status="正常",
            device_count=device_count,
            backup_count=backup_count,
            uptime="模拟运行时间",
            cpu_usage="15.2%",
            memory_usage="2.1GB / 8.0GB",
            disk_usage="45.6GB / 500GB"
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取系统信息失败: {str(e)}")

# ==================== 巡检功能 ====================

# 巡检任务存储（实际项目中应该使用数据库）
inspection_tasks: Dict[str, InspectionTask] = {}
inspection_lock = threading.Lock()

# 备份任务存储（实际项目中应该使用数据库）
backup_tasks: Dict[str, BackupTask] = {}
backup_lock = threading.Lock()

def perform_device_inspection(device: DBDevice) -> InspectionResult:
    """执行单个设备的巡检"""
    try:
        # 1. 检查设备在线状态
        online_status = asyncio.run(check_device_online(device.ip))

        details = {
            "online_status": online_status,
            "connectivity": "正常" if online_status else "失败",
            "response_time": "< 10ms" if online_status else "超时"
        }

        if not online_status:
            return InspectionResult(
                device_id=device.id,
                device_name=device.name,
                device_ip=device.ip,
                status="failed",
                message="设备离线，无法进行巡检",
                details=details,
                timestamp=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            )

        # 2. 尝试连接设备进行更详细的检查
        try:
            device_params = {
                'device_type': device.device_type,
                'host': device.ip,
                'username': device.username,
                'password': device.password,
                'port': device.port,
                'timeout': 10,
                'conn_timeout': 10
            }

            if device.remote_type == 'telnet':
                device_params['device_type'] = device_params['device_type'].replace('_ssh', '_telnet')

            # 建立连接
            connection = netmiko.ConnectHandler(**device_params)

            # 执行基本检查命令
            if 'cisco' in device.device_type.lower():
                version_output = connection.send_command("show version")
                interface_output = connection.send_command("show ip interface brief")
                details.update({
                    "version_info": version_output[:200] + "..." if len(version_output) > 200 else version_output,
                    "interface_status": "已获取" if interface_output else "获取失败"
                })
            elif 'huawei' in device.device_type.lower():
                version_output = connection.send_command("display version")
                interface_output = connection.send_command("display ip interface brief")
                details.update({
                    "version_info": version_output[:200] + "..." if len(version_output) > 200 else version_output,
                    "interface_status": "已获取" if interface_output else "获取失败"
                })
            else:
                # 通用命令
                try:
                    version_output = connection.send_command("show version")
                    details["version_info"] = version_output[:200] + "..." if len(version_output) > 200 else version_output
                except:
                    details["version_info"] = "无法获取版本信息"

            connection.disconnect()

            details.update({
                "ssh_connection": "成功",
                "authentication": "成功",
                "command_execution": "成功"
            })

            return InspectionResult(
                device_id=device.id,
                device_name=device.name,
                device_ip=device.ip,
                status="success",
                message="巡检完成，设备状态正常",
                details=details,
                timestamp=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            )

        except Exception as conn_error:
            details.update({
                "ssh_connection": "失败",
                "error_details": str(conn_error)
            })

            return InspectionResult(
                device_id=device.id,
                device_name=device.name,
                device_ip=device.ip,
                status="failed",
                message=f"设备连接失败: {str(conn_error)}",
                details=details,
                timestamp=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            )

    except Exception as e:
        return InspectionResult(
            device_id=device.id,
            device_name=device.name,
            device_ip=device.ip,
            status="failed",
            message=f"巡检过程中发生错误: {str(e)}",
            details={"error": str(e)},
            timestamp=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        )

def run_inspection_task(task_id: str, device_ids: List[int], db_session):
    """在后台执行巡检任务"""
    try:
        with inspection_lock:
            if task_id not in inspection_tasks:
                return

            task = inspection_tasks[task_id]
            task.status = InspectionStatus.RUNNING

        results = []
        completed = 0
        failed = 0

        for device_id in device_ids:
            try:
                # 获取设备信息
                device = db_session.query(DBDevice).filter(DBDevice.id == device_id).first()
                if not device:
                    failed += 1
                    continue

                # 执行巡检
                result = perform_device_inspection(device)
                results.append(result.dict())

                if result.status == "success":
                    completed += 1
                else:
                    failed += 1

                # 更新任务进度
                with inspection_lock:
                    if task_id in inspection_tasks:
                        task = inspection_tasks[task_id]
                        task.completed_devices = completed + failed
                        task.failed_devices = failed
                        task.progress = int((completed + failed) / len(device_ids) * 100)
                        task.results = results

            except Exception as e:
                failed += 1
                print(f"巡检设备 {device_id} 时发生错误: {str(e)}")

        # 任务完成
        with inspection_lock:
            if task_id in inspection_tasks:
                task = inspection_tasks[task_id]
                task.status = InspectionStatus.COMPLETED
                task.completed_at = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                task.progress = 100
                task.results = results

    except Exception as e:
        # 任务失败
        with inspection_lock:
            if task_id in inspection_tasks:
                task = inspection_tasks[task_id]
                task.status = InspectionStatus.FAILED
                task.error_message = str(e)
                task.completed_at = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"巡检任务 {task_id} 执行失败: {str(e)}")

def run_backup_task(task_id: str, device_ids: List[int], db_session):
    """在后台执行备份任务"""
    try:
        with backup_lock:
            if task_id not in backup_tasks:
                return

            task = backup_tasks[task_id]
            task.status = BackupStatus.RUNNING

        results = []
        completed = 0
        failed = 0

        for device_id in device_ids:
            try:
                # 获取设备信息
                device = db_session.query(DBDevice).filter(DBDevice.id == device_id).first()
                if not device:
                    failed += 1
                    continue

                # 执行备份
                backup_result = perform_backup(device)

                # 转换为BackupResult格式
                result = BackupResult(
                    device_id=device.id,
                    device_name=device.name,
                    device_ip=device.ip,
                    status=backup_result["status"],
                    message=backup_result["message"],
                    filename=backup_result.get("filename"),
                    timestamp=backup_result["timestamp"]
                )
                results.append(result.dict())

                # 保存备份记录到数据库
                backup_record = DBBackup(
                    device_id=backup_result["device_id"],
                    timestamp=backup_result["timestamp"],
                    status=backup_result["status"],
                    message=backup_result["message"],
                    filename=backup_result.get("filename", "")
                )
                db_session.add(backup_record)

                if backup_result["status"] == "success":
                    completed += 1
                else:
                    failed += 1

                # 更新任务进度
                with backup_lock:
                    if task_id in backup_tasks:
                        task = backup_tasks[task_id]
                        task.completed_devices = completed + failed
                        task.failed_devices = failed
                        task.progress = int((completed + failed) / len(device_ids) * 100)
                        task.results = results

            except Exception as e:
                failed += 1
                print(f"备份设备 {device_id} 时发生错误: {str(e)}")

        # 提交数据库事务
        db_session.commit()

        # 任务完成
        with backup_lock:
            if task_id in backup_tasks:
                task = backup_tasks[task_id]
                task.status = BackupStatus.COMPLETED
                task.completed_at = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                task.progress = 100
                task.results = results

        # 发送邮件通知（如果需要）
        try:
            send_backup_notification(results, "batch")
        except Exception as e:
            print(f"发送备份通知失败: {str(e)}")

    except Exception as e:
        # 任务失败
        with backup_lock:
            if task_id in backup_tasks:
                task = backup_tasks[task_id]
                task.status = BackupStatus.FAILED
                task.error_message = str(e)
                task.completed_at = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"备份任务 {task_id} 执行失败: {str(e)}")

# 单个设备巡检API
@app.post("/inspection/single", response_model=Dict[str, Any])
async def inspect_single_device(request: SingleInspectionRequest, background_tasks: BackgroundTasks, db: Session = Depends(get_db)):
    """单个设备巡检 - 异步执行"""
    try:
        # 检查设备是否存在
        device = db.query(DBDevice).filter(DBDevice.id == request.device_id).first()
        if not device:
            raise HTTPException(status_code=404, detail="设备不存在")

        # 创建巡检任务
        task_id = str(uuid.uuid4())
        task = InspectionTask(
            task_id=task_id,
            device_ids=[request.device_id],
            status=InspectionStatus.PENDING,
            total_devices=1,
            created_at=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        )

        with inspection_lock:
            inspection_tasks[task_id] = task

        # 启动后台任务
        background_tasks.add_task(run_inspection_task, task_id, [request.device_id], db)

        return {
            "success": True,
            "message": f"设备 {device.name} 的巡检任务已启动",
            "task_id": task_id,
            "device_name": device.name
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"启动巡检任务失败: {str(e)}")

# 批量设备巡检API
@app.post("/inspection/batch", response_model=Dict[str, Any])
async def inspect_batch_devices(request: InspectionRequest, background_tasks: BackgroundTasks, db: Session = Depends(get_db)):
    """批量设备巡检 - 异步执行"""
    try:
        if not request.device_ids:
            raise HTTPException(status_code=400, detail="设备ID列表不能为空")

        # 检查设备是否存在
        devices = db.query(DBDevice).filter(DBDevice.id.in_(request.device_ids)).all()
        if not devices:
            raise HTTPException(status_code=404, detail="没有找到指定的设备")

        existing_device_ids = [device.id for device in devices]
        missing_device_ids = set(request.device_ids) - set(existing_device_ids)

        if missing_device_ids:
            print(f"警告: 以下设备ID不存在: {missing_device_ids}")

        # 创建巡检任务
        task_id = str(uuid.uuid4())
        task = InspectionTask(
            task_id=task_id,
            device_ids=existing_device_ids,
            status=InspectionStatus.PENDING,
            total_devices=len(existing_device_ids),
            created_at=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        )

        with inspection_lock:
            inspection_tasks[task_id] = task

        # 启动后台任务
        background_tasks.add_task(run_inspection_task, task_id, existing_device_ids, db)

        return {
            "success": True,
            "message": f"批量巡检任务已启动，共 {len(existing_device_ids)} 个设备",
            "task_id": task_id,
            "total_devices": len(existing_device_ids),
            "device_names": [device.name for device in devices]
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"启动批量巡检任务失败: {str(e)}")

# 获取巡检任务状态API
@app.get("/inspection/task/{task_id}", response_model=InspectionTask)
async def get_inspection_task(task_id: str):
    """获取巡检任务状态"""
    with inspection_lock:
        if task_id not in inspection_tasks:
            raise HTTPException(status_code=404, detail="巡检任务不存在")
        return inspection_tasks[task_id]

# 获取所有巡检任务API
@app.get("/inspection/tasks", response_model=List[InspectionTask])
async def get_all_inspection_tasks():
    """获取所有巡检任务"""
    with inspection_lock:
        return list(inspection_tasks.values())

# 删除巡检任务API
@app.delete("/inspection/task/{task_id}")
async def delete_inspection_task(task_id: str):
    """删除巡检任务"""
    with inspection_lock:
        if task_id not in inspection_tasks:
            raise HTTPException(status_code=404, detail="巡检任务不存在")
        del inspection_tasks[task_id]
    return {"success": True, "message": "巡检任务已删除"}

# ==================== 异步备份功能 ====================

# 异步单个设备备份API
@app.post("/api/backup/single/async", response_model=Dict[str, Any])
async def async_single_backup_device(request: SingleBackupRequest, background_tasks: BackgroundTasks, db: Session = Depends(get_db)):
    """异步单个设备备份"""
    try:
        device_id = request.device_id

        # 检查设备是否存在
        device = db.query(DBDevice).filter(DBDevice.id == device_id).first()
        if not device:
            raise HTTPException(status_code=404, detail="设备不存在")

        # 创建备份任务
        task_id = str(uuid.uuid4())
        task = BackupTask(
            task_id=task_id,
            device_ids=[device_id],
            status=BackupStatus.PENDING,
            total_devices=1,
            created_at=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        )

        with backup_lock:
            backup_tasks[task_id] = task

        # 启动后台任务
        background_tasks.add_task(run_backup_task, task_id, [device_id], db)

        return {
            "success": True,
            "message": f"设备 {device.name} 备份任务已启动",
            "task_id": task_id,
            "total_devices": 1,
            "device_names": [device.name]
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"启动设备备份任务失败: {str(e)}")

# 异步批量设备备份API
@app.post("/api/backup/batch/async", response_model=Dict[str, Any])
async def async_batch_backup_devices(request: BatchBackupRequest, background_tasks: BackgroundTasks, db: Session = Depends(get_db)):
    """异步批量设备备份"""
    try:
        if not request.device_ids:
            raise HTTPException(status_code=400, detail="设备ID列表不能为空")

        # 检查设备是否存在
        devices = db.query(DBDevice).filter(DBDevice.id.in_(request.device_ids)).all()
        if not devices:
            raise HTTPException(status_code=404, detail="没有找到指定的设备")

        existing_device_ids = [device.id for device in devices]
        missing_device_ids = set(request.device_ids) - set(existing_device_ids)

        if missing_device_ids:
            print(f"警告: 以下设备ID不存在: {missing_device_ids}")

        # 创建备份任务
        task_id = str(uuid.uuid4())
        task = BackupTask(
            task_id=task_id,
            device_ids=existing_device_ids,
            status=BackupStatus.PENDING,
            total_devices=len(existing_device_ids),
            created_at=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        )

        with backup_lock:
            backup_tasks[task_id] = task

        # 启动后台任务
        background_tasks.add_task(run_backup_task, task_id, existing_device_ids, db)

        return {
            "success": True,
            "message": f"批量备份任务已启动，共 {len(existing_device_ids)} 个设备",
            "task_id": task_id,
            "total_devices": len(existing_device_ids),
            "device_names": [device.name for device in devices]
        }

    except Exception as e:
        print(f"批量备份失败: {e}")
        raise HTTPException(status_code=500, detail=f"批量备份失败: {str(e)}")

# ==================== 授权验证相关API ====================

# 预设的有效授权码（与前端保持一致）
VALID_LICENSE_CODES = {
    # 基础版授权码
    'BASIC-2024-NETW-TOOL': {
        'type': 'basic',
        'description': '基础版授权码',
        'features': []
    },
    'DEMO-BASIC-TEST-2024': {
        'type': 'basic',
        'description': '演示基础版授权码',
        'features': []
    },

    # 专业版授权码
    'PROF-2024-NETW-TOOL': {
        'type': 'professional',
        'description': '专业版授权码',
        'features': [
            'device_batch_backup', 'device_batch_inspection', 'device_batch_delete',
            'device_import_export', 'device_auto_backup', 'custom_commands',
            'config_comparison', 'advanced_settings', 'advanced_reports', 'performance_analysis'
        ]
    },
    'DEMO-PROF-TEST-2024': {
        'type': 'professional',
        'description': '演示专业版授权码',
        'features': [
            'device_batch_backup', 'device_batch_inspection', 'device_batch_delete',
            'device_import_export', 'device_auto_backup', 'custom_commands',
            'config_comparison', 'advanced_settings', 'advanced_reports', 'performance_analysis'
        ]
    }
}

# 添加批量生成的测试授权码
BATCH_BASIC_CODES = [
    'BAS-2024-YCSW-VLQQ', 'BASIC-2024-LDQN-U79T', 'BAS-2024-8N6Y-A6SS', 'BAS-2024-IKSB-SBP4', 'BASIC-2024-MIS0-C7LX',
    'BASIC-2025-GDO5-O3ZV', 'BASIC-2025-IG6J-AD87', 'BASIC-2025-QGW5-F7C8', 'BASIC-2024-0QB3-BVJ9', 'BAS-2024-1ER3-AQ6B',
    'BASIC-2024-XTN9-SCL4', 'BASIC-2025-Q79K-WVQL', 'BASIC-2024-I018-FDDW', 'BAS-2025-TZCB-4RYR', 'BASIC-2024-FCLI-WEXX',
    'BAS-2024-1592-KQKL', 'BAS-2024-OHGJ-9DNZ', 'BAS-2025-3GRM-FDV2', 'BAS-2025-P7MU-4JHU', 'BASIC-2025-MKB3-QV0W',
    'BAS-2024-WVDX-ZI6Z', 'BAS-2024-0OFE-IAJK', 'BAS-2025-WH5K-PH9N', 'BAS-2024-WV5T-1XMU', 'BAS-2025-95VP-QM62',
    'BASIC-2025-N9N6-IN95', 'BAS-2024-MQVA-DNVZ', 'BASIC-2025-FUAX-F415', 'BASIC-2025-JRPG-YILV', 'BAS-2024-Z8JT-UB9R',
    'BAS-2025-JPI4-NUEA', 'BASIC-2024-MGPD-7NLN', 'BAS-2025-KRF3-ONYJ', 'BASIC-2024-CEVG-4XWQ', 'BAS-2025-4C2K-716Q',
    'BAS-2025-DF60-2VY5', 'BASIC-2024-36BB-WIER', 'BAS-2025-WT7V-1HQ6', 'BASIC-2025-ZH83-33FG', 'BASIC-2024-WF43-BPIA',
    'BAS-2024-3CDV-HDYT', 'BAS-2024-Y2S5-AMSR', 'BASIC-2024-OA25-CZ63', 'BAS-2025-RN8Y-QM3P', 'BASIC-2024-HQ2M-ZX9L',
    'BAS-2025-XK7W-PV4N', 'BASIC-2024-JF5C-NB8R', 'BAS-2025-TG1B-VH6K', 'BASIC-2024-MZ9S-XQ2T', 'BAS-2025-QW3R-ER7Y',
    'BASIC-2024-4ZWD-K59I', 'BASIC-2024-MLCS-PSWQ', 'BASIC-2025-KQTD-NDVI', 'BASIC-2024-XXSH-J9FA', 'BASIC-2025-QCMZ-MD4M',
    'BASIC-2025-A8FR-CQWV', 'BASIC-2024-RHS4-EWG3', 'BASIC-2024-SNXA-CLBC', 'BASIC-2025-WCHU-ER0N', 'BASIC-2025-3PRN-S292',
    'BASIC-2025-BYIL-12IN', 'BASIC-2025-U8XI-SIDY', 'BASIC-2025-FT3P-9FB1', 'BASIC-2025-8135-K2AN', 'BASIC-2025-ZMVM-0I8A',
    'BASIC-2024-X4Y5-A7BE', 'BASIC-2025-NHKO-SF0Q', 'BASIC-2024-8FG1-7SZG', 'BASIC-2024-VDFJ-MZKE', 'BASIC-2025-QPQS-RDOF',
    'BASIC-2025-N0UU-4WHM', 'BASIC-2024-992Q-FR2X', 'BASIC-2025-X8T9-YZHC', 'BASIC-2024-EN4G-60IF', 'BASIC-2024-X23F-A38R',
    'BASIC-2025-KNUT-PU1D', 'BASIC-2024-QMCN-7OE7', 'BASIC-2025-A7BO-INIG', 'BASIC-2024-1234-5678', 'BASIC-2024-9101-1121',
    'BASIC-2024-1314-2425', 'BASIC-2024-1617-1819', 'BASIC-2024-2021-2223', 'BASIC-2024-2425-2627', 'BASIC-2024-2829-3031'
]

BATCH_PROFESSIONAL_CODES = [
    'PROF-2024-A1B2-C3D4', 'PRO-2025-E5F6-G7H8', 'PROF-2024-I9J0-K1L2', 'PRO-2025-M3N4-O5P6', 'PROF-2024-Q7R8-S9T0',
    'PRO-2025-U1V2-W3X4', 'PROF-2024-Y5Z6-A7B8', 'PRO-2025-C9D0-E1F2', 'PROF-2024-G3H4-I5J6', 'PRO-2025-K7L8-M9N0',
    'PROF-2024-O1P2-Q3R4', 'PRO-2025-S5T6-U7V8', 'PROF-2024-W9X0-Y1Z2', 'PRO-2025-A3B4-C5D6', 'PROF-2024-E7F8-G9H0',
    'PRO-2025-I1J2-K3L4', 'PROF-2024-M5N6-O7P8', 'PRO-2025-Q9R0-S1T2', 'PROF-2024-U3V4-W5X6', 'PRO-2025-Y7Z8-A9B0',
    'PROF-2024-C1D2-E3F4', 'PRO-2025-G5H6-I7J8', 'PROF-2024-K9L0-M1N2', 'PRO-2025-O3P4-Q5R6', 'PROF-2024-S7T8-U9V0',
    'PRO-2025-W1X2-Y3Z4', 'PROF-2024-A5B6-C7D8', 'PRO-2025-E9F0-G1H2', 'PROF-2024-I3J4-K5L6', 'PRO-2025-M7N8-O9P0',
    'PROF-2024-Q1R2-S3T4', 'PRO-2025-U5V6-W7X8', 'PROF-2024-Y9Z0-A1B2', 'PRO-2025-C3D4-E5F6', 'PROF-2024-G7H8-I9J0',
    'PRO-2025-K1L2-M3N4', 'PROF-2024-O5P6-Q7R8', 'PRO-2025-S9T0-U1V2', 'PROF-2024-W3X4-Y5Z6', 'PRO-2025-A7B8-C9D0',
    'PROF-2024-E1F2-G3H4', 'PRO-2025-I5J6-K7L8', 'PROF-2024-M9N0-O1P2', 'PRO-2025-Q3R4-S5T6', 'PROF-2024-U7V8-W9X0',
    'PRO-2025-Y1Z2-A3B4', 'PROF-2024-C5D6-E7F8', 'PRO-2025-G9H0-I1J2', 'PROF-2024-K3L4-M5N6', 'PRO-2025-O7P8-Q9R0',
    'PROF-2024-S1T2-U3V4', 'PRO-2025-W5X6-Y7Z8', 'PROF-2024-A9B0-C1D2', 'PRO-2025-E3F4-G5H6', 'PROF-2024-I7J8-K9L0',
    'PRO-2025-M1N2-O3P4', 'PROF-2024-Q5R6-S7T8', 'PRO-2025-U9V0-W1X2', 'PROF-2024-Y3Z4-A5B6', 'PRO-2025-C7D8-E9F0',
    'PROF-2024-G1H2-I3J4', 'PRO-2025-K5L6-M7N8', 'PROF-2024-O9P0-Q1R2', 'PRO-2025-S3T4-U5V6', 'PROF-2024-W7X8-Y9Z0',
    'PRO-2025-A1B2-C3D4', 'PROF-2024-E5F6-G7H8', 'PRO-2025-I9J0-K1L2', 'PRO-2024-M3N4-O5P6', 'PRO-2025-Q7R8-S9T0',
    'PRO-2024-U1V2-W3X4', 'PRO-2025-Y5Z6-A7B8', 'PRO-2024-C9D0-E1F2', 'PRO-2025-G3H4-I5J6', 'PRO-2024-K7L8-M9N0',
    'PRO-2025-O1P2-Q3R4', 'PRO-2024-S5T6-U7V8', 'PRO-2025-W9X0-Y1Z2', 'PRO-2024-A3B4-C5D6', 'PRO-2025-E7F8-G9H0',
    'PRO-2024-I1J2-K3L4', 'PRO-2025-M5N6-O7P8', 'PRO-2024-Q9R0-S1T2', 'PRO-2025-U3V4-W5X6', 'PRO-2024-Y7Z8-A9B0',
    'PRO-2025-C1D2-E3F4', 'PROF-2024-G5H6-I7J8', 'PRO-2025-K9L0-M1N2', 'PRO-2024-O3P4-Q5R6', 'PRO-2025-S7T8-U9V0',
    'PRO-2024-W1X2-Y3Z4', 'PRO-2025-A5B6-C7D8', 'PRO-2024-E9F0-G1H2', 'PRO-2025-I3J4-K5L6', 'PRO-2024-M7N8-O9P0',
    'PRO-2025-Q1R2-S3T4', 'PRO-2024-U5V6-W7X8', 'PRO-2025-Y9Z0-A1B2', 'PRO-2024-C3D4-E5F6', 'PRO-2025-G7H8-I9J0',
    'PRO-2024-K1L2-M3N4', 'PRO-2025-O5P6-Q7R8', 'PRO-2024-S9T0-U1V2', 'PRO-2025-W3X4-Y5Z6', 'PRO-2024-A7B8-C9D0'
]

# 合并所有授权码
for code in BATCH_BASIC_CODES:
    VALID_LICENSE_CODES[code] = {
        'type': 'basic',
        'description': '基础版测试授权码',
        'features': []
    }

for code in BATCH_PROFESSIONAL_CODES:
    VALID_LICENSE_CODES[code] = {
        'type': 'professional',
        'description': '专业版测试授权码',
        'features': [
            'device_batch_backup', 'device_batch_inspection', 'device_batch_delete',
            'device_import_export', 'device_auto_backup', 'custom_commands',
            'config_comparison', 'advanced_settings', 'advanced_reports', 'performance_analysis'
        ]
    }

# 授权验证核心函数
def validate_license_code(license_code: str) -> Dict[str, Any]:
    """验证授权码格式和有效性"""
    if not license_code or not isinstance(license_code, str):
        return {"valid": False, "error": "授权码不能为空", "error_code": "EMPTY_CODE"}

    normalized_code = license_code.strip().upper()

    if normalized_code in VALID_LICENSE_CODES:
        license_info = VALID_LICENSE_CODES[normalized_code]
        return {
            "valid": True,
            "type": license_info["type"],
            "description": license_info["description"],
            "features": license_info["features"]
        }

    return {"valid": False, "error": "无效的授权码", "error_code": "INVALID_CODE"}

def log_verification_attempt(db: Session, license_id: int, device_fingerprint: str,
                           result: str, error_message: str = None,
                           ip_address: str = None, user_agent: str = None):
    """记录授权验证日志"""
    try:
        log_entry = DBLicenseVerificationLog(
            license_id=license_id,
            device_fingerprint=device_fingerprint,
            verification_result=result,
            error_message=error_message,
            ip_address=ip_address,
            user_agent=user_agent
        )
        db.add(log_entry)
        db.commit()
    except Exception as e:
        print(f"记录验证日志失败: {e}")
        try:
            db.rollback()
        except:
            pass  # 忽略回滚错误

# 授权激活API
@app.post("/api/license/activate", response_model=LicenseResponse)
async def activate_license(request: LicenseActivationRequest, db: Session = Depends(get_db)):
    """激活授权码"""
    print(f"🚀 收到授权激活请求: {request.license_code}, 设备指纹: {request.device_fingerprint}")
    try:
        # 验证授权码
        validation = validate_license_code(request.license_code)
        print(f"📋 授权码验证结果: {validation}")
        if not validation["valid"]:
            return LicenseResponse(
                success=False,
                message=validation["error"],
                error_code=validation.get("error_code")
            )

        # 检查授权码是否已被激活
        existing_license = db.query(DBLicense).filter(
            DBLicense.license_code == request.license_code.strip().upper()
        ).first()

        if existing_license:
            # 检查设备指纹是否匹配
            # 如果设备指纹为None（已重置），则允许重新绑定到新设备
            if existing_license.device_fingerprint is not None and existing_license.device_fingerprint != request.device_fingerprint:
                log_verification_attempt(
                    db, existing_license.id, request.device_fingerprint,
                    "failed", "设备指纹不匹配"
                )
                return LicenseResponse(
                    success=False,
                    message="此授权码已在其他设备上激活",
                    error_code="DEVICE_MISMATCH"
                )

            # 如果设备指纹为None，更新为当前设备指纹（重新绑定）
            if existing_license.device_fingerprint is None:
                existing_license.device_fingerprint = request.device_fingerprint
                print(f"重新绑定授权码 {request.license_code} 到设备: {request.device_fingerprint}")

            # 更新现有授权
            existing_license.last_verified_at = datetime.utcnow()
            existing_license.verification_count += 1
            existing_license.is_active = True
            db.commit()

            log_verification_attempt(
                db, existing_license.id, request.device_fingerprint, "success"
            )

            return LicenseResponse(
                success=True,
                license_type=existing_license.license_type,
                message="授权激活成功！",
                features=validation["features"]
            )

        # 创建新的授权记录
        new_license = DBLicense(
            license_code=request.license_code.strip().upper(),
            license_type=validation["type"],
            device_fingerprint=request.device_fingerprint,
            verification_count=1
        )

        db.add(new_license)
        db.commit()
        db.refresh(new_license)

        log_verification_attempt(
            db, new_license.id, request.device_fingerprint, "success"
        )

        return LicenseResponse(
            success=True,
            license_type=validation["type"],
            message="授权激活成功！",
            features=validation["features"]
        )

    except Exception as e:
        print(f"授权激活失败: {e}")
        db.rollback()
        return LicenseResponse(
            success=False,
            message="授权激活过程中发生错误",
            error_code="INTERNAL_ERROR"
        )

# 授权验证API
@app.post("/api/license/verify", response_model=LicenseResponse)
async def verify_license(request: LicenseVerificationRequest, db: Session = Depends(get_db)):
    """验证授权码有效性"""
    try:
        # 查找授权记录
        license_record = db.query(DBLicense).filter(
            DBLicense.license_code == request.license_code.strip().upper(),
            DBLicense.is_active == True
        ).first()

        if not license_record:
            return LicenseResponse(
                success=False,
                message="授权码不存在或已失效",
                error_code="LICENSE_NOT_FOUND"
            )

        # 验证设备指纹
        if license_record.device_fingerprint != request.device_fingerprint:
            log_verification_attempt(
                db, license_record.id, request.device_fingerprint,
                "failed", "设备指纹不匹配"
            )
            return LicenseResponse(
                success=False,
                message="设备指纹不匹配，请在原设备上使用",
                error_code="DEVICE_MISMATCH"
            )

        # 检查是否过期（如果设置了过期时间）
        if license_record.expires_at and license_record.expires_at < datetime.utcnow():
            log_verification_attempt(
                db, license_record.id, request.device_fingerprint,
                "failed", "授权已过期"
            )
            return LicenseResponse(
                success=False,
                message="授权已过期",
                error_code="LICENSE_EXPIRED"
            )

        # 更新验证信息
        license_record.last_verified_at = datetime.utcnow()
        license_record.verification_count += 1
        db.commit()

        # 获取功能列表
        validation = validate_license_code(request.license_code)
        features = validation.get("features", []) if validation["valid"] else []

        log_verification_attempt(
            db, license_record.id, request.device_fingerprint, "success"
        )

        return LicenseResponse(
            success=True,
            license_type=license_record.license_type,
            message="授权验证成功",
            expires_at=license_record.expires_at.isoformat() if license_record.expires_at else None,
            features=features
        )

    except Exception as e:
        print(f"授权验证失败: {e}")
        return LicenseResponse(
            success=False,
            message="授权验证过程中发生错误",
            error_code="INTERNAL_ERROR"
        )

# 获取授权信息API
@app.get("/api/license/info/{license_code}")
async def get_license_info(license_code: str, db: Session = Depends(get_db)):
    """获取授权信息"""
    try:
        license_record = db.query(DBLicense).filter(
            DBLicense.license_code == license_code.strip().upper()
        ).first()

        if not license_record:
            raise HTTPException(status_code=404, detail="授权码不存在")

        return LicenseInfo(
            id=license_record.id,
            license_code=license_record.license_code,
            license_type=license_record.license_type,
            device_fingerprint=license_record.device_fingerprint,
            activated_at=license_record.activated_at.isoformat(),
            expires_at=license_record.expires_at.isoformat() if license_record.expires_at else None,
            is_active=license_record.is_active,
            last_verified_at=license_record.last_verified_at.isoformat(),
            verification_count=license_record.verification_count
        )

    except HTTPException:
        raise
    except Exception as e:
        print(f"获取授权信息失败: {e}")
        raise HTTPException(status_code=500, detail="获取授权信息失败")

# 撤销授权API
@app.post("/api/license/revoke/{license_code}")
async def revoke_license(license_code: str, db: Session = Depends(get_db)):
    """撤销授权"""
    try:
        license_record = db.query(DBLicense).filter(
            DBLicense.license_code == license_code.strip().upper()
        ).first()

        if not license_record:
            raise HTTPException(status_code=404, detail="授权码不存在")

        license_record.is_active = False
        db.commit()

        return {"success": True, "message": "授权已撤销"}

    except HTTPException:
        raise
    except Exception as e:
        print(f"撤销授权失败: {e}")
        raise HTTPException(status_code=500, detail="撤销授权失败")

# 重置授权码设备绑定API
@app.post("/api/license/reset/{license_code}")
async def reset_license_device_binding(license_code: str, db: Session = Depends(get_db)):
    """重置授权码的设备绑定，允许在新设备上重新激活"""
    try:
        license_record = db.query(DBLicense).filter(
            DBLicense.license_code == license_code.strip().upper()
        ).first()

        if not license_record:
            raise HTTPException(status_code=404, detail="授权码不存在")

        # 清除设备指纹，重置为可重新激活状态
        old_fingerprint = license_record.device_fingerprint
        license_record.device_fingerprint = None
        license_record.is_active = True  # 确保授权是激活状态
        db.commit()

        print(f"重置授权码 {license_code} 的设备绑定，原设备指纹: {old_fingerprint}")

        return {
            "success": True,
            "message": f"授权码 {license_code} 的设备绑定已重置，可在新设备上重新激活",
            "old_device_fingerprint": old_fingerprint
        }

    except HTTPException:
        raise
    except Exception as e:
        print(f"重置授权码设备绑定失败: {e}")
        raise HTTPException(status_code=500, detail="重置授权码设备绑定失败")

# 启动时初始化数据库
@app.on_event("startup")
async def startup_event():
    """应用启动时的初始化操作"""
    try:
        # 执行数据库迁移
        migrate_database()

        # 初始化数据库表
        init_db()

        print("✅ 数据库初始化完成")
        # print(f"✅ 已加载 {len(VALID_LICENSE_CODES)} 个有效授权码")

    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8090)

# 获取备份任务状态API
@app.get("/api/backup/task/{task_id}", response_model=BackupTask)
async def get_backup_task(task_id: str):
    """获取备份任务状态"""
    with backup_lock:
        if task_id not in backup_tasks:
            raise HTTPException(status_code=404, detail="备份任务不存在")
        return backup_tasks[task_id]

# 获取所有备份任务API
@app.get("/api/backup/tasks", response_model=List[BackupTask])
async def get_all_backup_tasks():
    """获取所有备份任务"""
    with backup_lock:
        return list(backup_tasks.values())

# 删除备份任务API
@app.delete("/api/backup/task/{task_id}")
async def delete_backup_task(task_id: str):
    """删除备份任务"""
    with backup_lock:
        if task_id not in backup_tasks:
            raise HTTPException(status_code=404, detail="备份任务不存在")
        del backup_tasks[task_id]
    return {"success": True, "message": "备份任务已删除"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8090) # 统一使用8090端口