<template>
  <a-config-provider :locale="zhCN">
    <!-- 主应用界面 - 现在由路由守卫控制授权 -->
    <div class="layout">
      <a-layout>
        <a-layout-header class="header">
          <div class="logo">
            <a-typography-title :heading="3" style="margin: 0; color: #fff;">
              网络设备备份工具
            </a-typography-title>
          </div>
          <div class="header-right">
            <!-- 基础版标识 -->
            <div v-if="isBasicComputed" class="basic-badge">
              <icon-trophy />
              <span>基础版</span>
            </div>

            <!-- VIP标识 -->
            <div v-if="isProfessionalComputed" class="vip-badge">
              <icon-trophy />
              <span>VIP</span>
            </div>

            <!-- 授权状态指示器 -->
            <div class="license-status">
              <div class="status-indicator" :class="{ 'verified': isLicenseVerified }">
                <icon-check-circle v-if="isLicenseVerified" />
                <icon-close-circle v-else />
              </div>
              <span class="status-text">
                {{ isLicenseVerified ? '已验证' : '验证中' }}
              </span>
            </div>

            <!-- 升级按钮 -->
            <a-button
              v-if="isBasicComputed"
              type="primary"
              status="warning"
              size="small"
              @click="showUpgradeModal"
              class="upgrade-btn"
            >
              <template #icon><icon-star /></template>
              升级专业版
            </a-button>
            <!-- 版本对比按钮 -->
            <a-button
              type="outline"
              size="small"
              @click="showComparisonModal"
              class="comparison-btn"
              style="color: white; border-color: rgba(255,255,255,0.8); background: rgba(255,255,255,0.1);"
            >
              <template #icon><icon-info-circle /></template>
              版本对比
            </a-button>

            <!-- 重新授权按钮 -->
            <a-button
              type="outline"
              size="small"
              @click="showReauthModal"
              class="reauth-btn"
              style="color: white; border-color: rgba(255,255,255,0.8); background: rgba(255,255,255,0.1);"
            >
              <template #icon><icon-settings /></template>
              重新授权
            </a-button>
          </div>
        </a-layout-header>
        <a-layout>
          <a-layout-sider
            collapsible
            :collapsed="collapsed"
            :collapsible="true"
            :width="220"
            breakpoint="xl"
            @collapse="handleCollapse"
            class="layout-sider"
            style="background: var(--color-bg-2);"
          >
            <a-menu
              :default-selected-keys="[currentRoute]"
              :style="{ width: '100%', height: '100%' }"
              @menu-item-click="onMenuClick"
              :collapsed="collapsed"
            >
              <a-menu-item key="devices">
                <template #icon><icon-desktop /></template>
                设备管理
              </a-menu-item>
              <a-menu-item key="backups">
                <template #icon><icon-storage /></template>
                配置备份
              </a-menu-item>
              <a-menu-item
                key="commands"
                :disabled="!hasCustomCommandsFeature"
                @click="handleCommandsClick"
              >
                <template #icon><icon-code /></template>
                自定义命令
                <a-tag v-if="!hasCustomCommandsFeature" color="orange" size="mini" style="margin-left: 8px;">
                  专业版
                </a-tag>
              </a-menu-item>
              <a-menu-item key="comparison">
                <template #icon><icon-code-square /></template>
                配置对比
              </a-menu-item>
              <a-menu-item key="settings">
                <template #icon><icon-settings /></template>
                系统设置
              </a-menu-item>
              <a-menu-item key="test">
                <template #icon><icon-experiment /></template>
                测试页面
              </a-menu-item>
            </a-menu>
          </a-layout-sider>
          <a-layout-content class="content">
            <a-breadcrumb :style="{ margin: '16px 0' }">
              <a-breadcrumb-item>首页</a-breadcrumb-item>
              <a-breadcrumb-item>{{ currentPath }}</a-breadcrumb-item>
            </a-breadcrumb>
            <div class="content-container">
              <router-view></router-view>
            </div>
          </a-layout-content>
        </a-layout>
        <!-- <a-layout-footer class="footer">
          网络设备备份工具 ©2025
        </a-layout-footer> -->
      </a-layout>
    </div>

    <!-- 升级弹窗 -->
    <!-- <LicenseUpgrade
      v-model="upgradeModalVisible"
      @success="handleUpgradeSuccess"
    /> -->

    <!-- 版本对比弹窗 -->
    <VersionComparison
      v-model="comparisonModalVisible"
      :show-success-message="showUpgradeSuccess"
    />
  </a-config-provider>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { Message } from '@arco-design/web-vue'
import {
  IconDesktop,
  IconStorage,
  IconCodeSquare,
  IconSettings,
  IconExperiment,
  IconCode,
  IconTrophy,
  IconStar,
  IconInfoCircle,
  IconCheckCircle,
  IconCloseCircle
} from '@arco-design/web-vue/es/icon'
import zhCN from '@arco-design/web-vue/es/locale/lang/zh-cn'
import {
  enhancedLicenseManager,
  hasAnyLicense,
  isBasic,
  isProfessional,
  hasFeature,
  FEATURES,
  getLicenseInfo
} from '@/utils/enhanced-license'
import VersionComparison from '@/components/VersionComparison.vue'
import { forceReauthorization } from '@/router/license-guard.js'

const router = useRouter()
const route = useRoute()
const collapsed = ref(false)

// 授权相关
const comparisonModalVisible = ref(false)
const showUpgradeSuccess = ref(false)
const isLicenseVerified = ref(false)

const currentRoute = computed(() => {
  return route.name || 'devices'
})

const currentPath = computed(() => {
  const pathMap = {
    'devices': '设备管理',
    'backups': '配置备份',
    'commands': '自定义命令',
    'comparison': '配置对比',
    'settings': '系统设置',
    'test': '测试页面'
  }
  return pathMap[route.name] || '首页'
})

// 响应式授权状态 - 用于强制更新computed属性
const licenseStateVersion = ref(0)

// 授权状态计算属性
const hasAnyLicenseComputed = computed(() => {
  licenseStateVersion.value // 依赖这个响应式值来触发重新计算
  return hasAnyLicense()
})
const isBasicComputed = computed(() => {
  licenseStateVersion.value // 依赖这个响应式值来触发重新计算
  return isBasic()
})
const isProfessionalComputed = computed(() => {
  licenseStateVersion.value // 依赖这个响应式值来触发重新计算
  return isProfessional()
})

// 授权相关计算属性
const hasCustomCommandsFeature = computed(() => {
  return hasFeature(FEATURES.CUSTOM_COMMANDS)
})

const handleCollapse = (val) => {
  collapsed.value = val
}

const onMenuClick = (key) => {
  router.push({ name: key })
}

// 处理自定义命令点击
const handleCommandsClick = (e) => {
  if (!hasCustomCommandsFeature.value) {
    e.preventDefault()
    Message.warning('自定义命令功能需要专业版授权')
    showUpgradeModal()
    return false
  } else {
    // 专业版用户可以正常访问
    router.push({ name: 'commands' })
  }
}

// 显示升级弹窗
const showUpgradeModal = () => {
  router.push({
    name: 'license-gate',
    query: {
      redirect: route.fullPath,
      reason: 'upgrade'
    }
  })
}

// 显示重新授权弹窗
const showReauthModal = () => {
  forceReauthorization('manual')
}

// 显示版本对比弹窗
const showComparisonModal = () => {
  comparisonModalVisible.value = true
}

// 检查授权状态
const checkLicenseStatus = async () => {
  try {
    isLicenseVerified.value = enhancedLicenseManager.hasValidLicense()

    if (isLicenseVerified.value) {
      // 验证服务器状态
      const serverValid = await enhancedLicenseManager.verifyWithServer()
      isLicenseVerified.value = serverValid
    }
  } catch (error) {
    console.error('检查授权状态失败:', error)
    isLicenseVerified.value = false
  }
}

// 处理升级成功
const handleUpgradeSuccess = (result) => {
  showUpgradeSuccess.value = true
  comparisonModalVisible.value = true

  // 3秒后隐藏成功消息
  setTimeout(() => {
    showUpgradeSuccess.value = false
  }, 3000)

  // 刷新页面以应用新的授权状态
  setTimeout(() => {
    window.location.reload()
  }, 1000)
}

// 生命周期钩子
onMounted(async () => {
  // 初始化授权状态检查
  await checkLicenseStatus()

  // 监听授权状态变化
  window.addEventListener('license-activated', checkLicenseStatus)
  window.addEventListener('license-verification-failed', () => {
    isLicenseVerified.value = false
  })

  // 监听授权状态变化事件，强制更新computed属性
  const handleLicenseStateChange = () => {
    console.log('🔄 授权状态变化，强制更新UI')
    licenseStateVersion.value++
    checkLicenseStatus()
  }
  window.addEventListener('license-state-changed', handleLicenseStateChange)

  // 定期检查授权状态
  const statusCheckInterval = setInterval(checkLicenseStatus, 60000) // 每分钟检查一次

  // 清理定时器
  onUnmounted(() => {
    clearInterval(statusCheckInterval)
    window.removeEventListener('license-activated', checkLicenseStatus)
    window.removeEventListener('license-verification-failed', () => {
      isLicenseVerified.value = false
    })
    window.removeEventListener('license-state-changed', handleLicenseStateChange)
  })
})
</script>

<style scoped>
.layout {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  background-color: rgb(var(--primary-6));
}

.logo {
  height: 100%;
  display: flex;
  align-items: center;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.basic-badge {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  color: white;
  border-radius: 20px;
  font-weight: bold;
  font-size: 14px;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
}

.basic-badge .arco-icon {
  font-size: 16px;
  color: white;
}

.vip-badge {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
  color: #333;
  border-radius: 20px;
  font-weight: bold;
  font-size: 14px;
  box-shadow: 0 2px 8px rgba(255, 215, 0, 0.3);
}

.vip-badge .arco-icon {
  font-size: 16px;
  color: #ff6b00;
}

.upgrade-btn {
  border-radius: 20px;
  font-weight: bold;
  box-shadow: 0 2px 8px rgba(255, 125, 0, 0.3);
}

.comparison-btn,
.reauth-btn {
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.6);
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.1);
}

.comparison-btn:hover,
.reauth-btn:hover {
  color: white;
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.8);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(255, 255, 255, 0.2);
}

.license-status {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.status-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  transition: all 0.3s;
}

.status-indicator.verified {
  background: rgba(82, 196, 26, 0.2);
  color: #52c41a;
}

.status-indicator:not(.verified) {
  background: rgba(255, 193, 7, 0.2);
  color: #ffc107;
  animation: pulse 2s infinite;
}

.status-text {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.6; }
  100% { opacity: 1; }
}

.content {
  padding: 0 24px;
  background: var(--color-fill-2);
  flex: 1;
  overflow: hidden;
}

.content-container {
  background: var(--color-bg-2);
  padding: 24px;
  border-radius: 4px;
  height: calc(100% - 64px); /* 减去面包屑的高度 */
  overflow-y: auto;
}

.footer {
  text-align: center;
  padding: 16px;
  color: var(--color-text-3);
}

.layout-sider {
  height: calc(100vh - 64px); /* 减去顶部导航栏的高度 */
  overflow-y: hidden; /* 移除滚动条 */
}

/* 解决内容可能溢出的问题 */
:deep(.arco-layout) {
  height: 100vh;
}

:deep(.arco-layout-sider-children) {
  height: 100%;
  display: flex;
  flex-direction: column;
}

:deep(.arco-menu) {
  flex: 1;
  overflow: hidden; /* 确保菜单不会显示滚动条 */
}
</style> 