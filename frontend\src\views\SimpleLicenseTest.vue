<template>
  <div class="simple-license-test">
    <div class="test-container">
      <h1>授权系统测试</h1>
      
      <!-- 当前状态 -->
      <div class="status-section">
        <h2>当前授权状态</h2>
        <div class="status-info">
          <p><strong>版本类型：</strong> {{ currentLicenseType }}</p>
          <p><strong>是否专业版：</strong> {{ isProfessional ? '是' : '否' }}</p>
          <p><strong>授权码：</strong> {{ licenseInfo.code || '未激活' }}</p>
          <p><strong>激活时间：</strong> {{ licenseInfo.activatedAt ? new Date(licenseInfo.activatedAt).toLocaleString() : '未激活' }}</p>
          <p><strong>可用功能数：</strong> {{ licenseInfo.features ? licenseInfo.features.length : 0 }}</p>
        </div>
      </div>

      <!-- 功能测试 -->
      <div class="features-section">
        <h2>功能权限测试</h2>
        <div class="feature-list">
          <div v-for="(feature, key) in FEATURES" :key="key" class="feature-item">
            <span class="feature-name">{{ FEATURE_DESCRIPTIONS[feature] }}</span>
            <span class="feature-status" :class="{ 'authorized': hasFeature(feature), 'unauthorized': !hasFeature(feature) }">
              {{ hasFeature(feature) ? '✓ 已授权' : '✗ 未授权' }}
            </span>
            <button 
              class="test-btn" 
              :disabled="!hasFeature(feature)"
              @click="testFeature(feature)"
            >
              测试
            </button>
          </div>
        </div>
      </div>

      <!-- 授权操作 -->
      <div class="actions-section">
        <h2>授权操作</h2>
        <div class="action-buttons">
          <button v-if="!isProfessional" class="upgrade-btn" @click="showUpgradeForm">
            升级到专业版
          </button>
          <button class="reset-btn" v-if="isProfessional" @click="resetLicense">
            重置为基础版
          </button>
          <button class="refresh-btn" @click="refreshStatus">
            刷新状态
          </button>
        </div>
      </div>

      <!-- 升级表单 -->
      <div v-if="showUpgrade" class="upgrade-section">
        <h2>输入授权码</h2>
        <div class="upgrade-form">
          <input 
            v-model="licenseCode" 
            type="text" 
            placeholder="请输入专业版授权码"
            class="license-input"
          />
          <button class="activate-btn" @click="activateLicense" :disabled="!licenseCode.trim()">
            激活授权
          </button>
          <button class="cancel-btn" @click="showUpgrade = false">
            取消
          </button>
        </div>
        
        <div class="demo-codes">
          <h3>演示授权码：</h3>
          <div class="code-list">
            <div class="code-item" @click="copyCode('PROF-2024-NETW-TOOL')">
              <code>PROF-2024-NETW-TOOL</code>
              <span class="copy-hint">点击复制</span>
            </div>
            <div class="code-item" @click="copyCode('DEMO-PROF-TEST-2024')">
              <code>DEMO-PROF-TEST-2024</code>
              <span class="copy-hint">点击复制</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 消息显示 -->
      <div v-if="message" class="message" :class="messageType">
        {{ message }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import {
  isProfessional,
  hasFeature,
  FEATURES,
  FEATURE_DESCRIPTIONS,
  getLicenseInfo,
  enhancedLicenseManager
} from '@/utils/enhanced-license'

// 响应式数据
const licenseInfo = ref({})
const showUpgrade = ref(false)
const licenseCode = ref('')
const message = ref('')
const messageType = ref('info')

// 计算属性
const currentLicenseType = computed(() => {
  return isProfessional() ? '专业版' : '基础版'
})

// 方法
const refreshStatus = () => {
  licenseInfo.value = getLicenseInfo()
  showMessage('状态已刷新', 'success')
}

const testFeature = (feature) => {
  if (hasFeature(feature)) {
    showMessage(`功能 "${FEATURE_DESCRIPTIONS[feature]}" 测试成功！`, 'success')
  } else {
    showMessage(`功能 "${FEATURE_DESCRIPTIONS[feature]}" 未授权！`, 'error')
  }
}

const showUpgradeForm = () => {
  showUpgrade.value = true
}

const activateLicense = async () => {
  if (!licenseCode.value.trim()) {
    showMessage('请输入授权码', 'error')
    return
  }

  try {
    const result = await enhancedLicenseManager.activateLicense(licenseCode.value)
    
    if (result.success) {
      showMessage(result.message, 'success')
      showUpgrade.value = false
      licenseCode.value = ''
      refreshStatus()
    } else {
      showMessage(result.error || '授权激活失败', 'error')
    }
  } catch (error) {
    console.error('升级失败:', error)
    showMessage('升级过程中发生错误，请重试', 'error')
  }
}

const resetLicense = () => {
  enhancedLicenseManager.clearLicense()
  refreshStatus()
  showMessage('已重置为基础版', 'success')
}

const copyCode = async (code) => {
  try {
    await navigator.clipboard.writeText(code)
    showMessage('授权码已复制到剪贴板', 'success')
    licenseCode.value = code
  } catch (error) {
    console.error('复制失败:', error)
    showMessage('复制失败，请手动复制', 'error')
  }
}

const showMessage = (msg, type = 'info') => {
  message.value = msg
  messageType.value = type
  setTimeout(() => {
    message.value = ''
  }, 3000)
}

// 生命周期
onMounted(() => {
  refreshStatus()
})
</script>

<style scoped>
.simple-license-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-container {
  background: white;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

h1 {
  color: #333;
  text-align: center;
  margin-bottom: 30px;
}

h2 {
  color: #555;
  border-bottom: 2px solid #e8e8e8;
  padding-bottom: 10px;
  margin: 30px 0 20px 0;
}

.status-section, .features-section, .actions-section, .upgrade-section {
  margin-bottom: 30px;
}

.status-info p {
  margin: 8px 0;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 4px;
}

.feature-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.feature-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  background: #fafafa;
}

.feature-name {
  flex: 1;
  font-weight: 500;
}

.feature-status {
  margin: 0 15px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 14px;
}

.feature-status.authorized {
  background: #d4edda;
  color: #155724;
}

.feature-status.unauthorized {
  background: #f8d7da;
  color: #721c24;
}

.test-btn {
  padding: 6px 12px;
  border: 1px solid #007bff;
  background: #007bff;
  color: white;
  border-radius: 4px;
  cursor: pointer;
}

.test-btn:disabled {
  background: #6c757d;
  border-color: #6c757d;
  cursor: not-allowed;
}

.action-buttons {
  display: flex;
  gap: 15px;
}

.upgrade-btn, .reset-btn, .refresh-btn, .activate-btn, .cancel-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
}

.upgrade-btn {
  background: #28a745;
  color: white;
}

.reset-btn {
  background: #dc3545;
  color: white;
}

.refresh-btn {
  background: #007bff;
  color: white;
}

.activate-btn {
  background: #28a745;
  color: white;
}

.cancel-btn {
  background: #6c757d;
  color: white;
}

.upgrade-form {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.license-input {
  flex: 1;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.demo-codes {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.code-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.code-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.code-item:hover {
  background: #e9ecef;
}

.code-item code {
  font-family: 'Courier New', monospace;
  background: #f6f8fa;
  padding: 4px 8px;
  border-radius: 3px;
  border: 1px solid #d0d7de;
}

.copy-hint {
  font-size: 12px;
  color: #666;
}

.message {
  padding: 12px;
  border-radius: 6px;
  margin-top: 20px;
  font-weight: 500;
}

.message.success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.message.error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.message.info {
  background: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}
</style>
