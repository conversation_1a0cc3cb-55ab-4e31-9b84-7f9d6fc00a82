<template>
  <div class="enhanced-license-gate">
    <div class="license-container">
      <!-- 头部 -->
      <div class="license-header">
        <div class="logo-section">
          <div class="logo-icon">🔐</div>
          <h1>网络设备备份工具</h1>
          <p class="subtitle">请输入有效的授权码以继续使用</p>
        </div>
      </div>

      <!-- 错误提示 -->
      <div v-if="errorMessage && errorCode !== 'SUCCESS'" class="error-banner">
        <icon-close-circle-fill />
        <div class="error-content">
          <h3>授权验证失败</h3>
          <p>{{ errorMessage }}</p>
          <p v-if="errorCode" class="error-code">错误代码: {{ errorCode }}</p>
        </div>
      </div>

      <!-- 成功提示 -->
      <div v-if="errorMessage && errorCode === 'SUCCESS'" class="success-banner">
        <icon-check-circle-fill />
        <div class="success-content">
          <h3>授权验证成功</h3>
          <p>{{ errorMessage }}</p>
          <p class="redirect-info">正在跳转到设备管理界面...</p>
        </div>
      </div>

      <!-- 授权表单 -->
      <div class="license-form">
        <div class="form-group">
          <label for="licenseCode">授权码</label>
          <div class="input-wrapper">
            <input
              id="licenseCode"
              v-model="licenseCode"
              type="text"
              placeholder="请输入授权码，例如：PROF-2024-NETW-TOOL"
              class="license-input"
              :disabled="isActivating"
              @keyup.enter="activateLicense"
              @input="clearError"
            />
            <div v-if="isActivating" class="input-loading">
              <icon-loading />
            </div>
          </div>
          <div class="input-hint">
            授权码格式：(BASIC|BAS|PROF|PRO)-YYYY-XXXX-XXXX
          </div>
        </div>

        <div class="form-actions">
          <button
            class="activate-btn"
            :disabled="!licenseCode.trim() || isActivating"
            @click="activateLicense"
          >
            <icon-loading v-if="isActivating" />
            <icon-lock v-else />
            {{ isActivating ? '正在验证...' : '激活授权' }}
          </button>
        </div>
      </div>

      <!-- 示例授权码 -->
      <div class="demo-section">
        <h3>测试授权码</h3>
        <div class="demo-tabs">
          <button
            class="tab-btn"
            :class="{ active: activeTab === 'basic' }"
            @click="activeTab = 'basic'"
          >
            基础版
          </button>
          <button
            class="tab-btn"
            :class="{ active: activeTab === 'professional' }"
            @click="activeTab = 'professional'"
          >
            专业版
          </button>
        </div>

        <div class="demo-codes">
          <div v-if="activeTab === 'basic'" class="code-list">
            <div
              v-for="code in basicCodes"
              :key="code"
              class="code-item basic"
              @click="selectCode(code)"
            >
              <code>{{ code }}</code>
              <span class="copy-hint">点击使用</span>
            </div>
          </div>

          <div v-if="activeTab === 'professional'" class="code-list">
            <div
              v-for="code in professionalCodes"
              :key="code"
              class="code-item professional"
              @click="selectCode(code)"
            >
              <code>{{ code }}</code>
              <span class="copy-hint">点击使用</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 版本对比 -->
      <div class="version-info">
        <h3>版本功能对比</h3>
        <div class="version-grid">
          <div class="version-card basic">
            <h4>基础版</h4>
            <ul>
              <li>✓ 设备管理</li>
              <li>✓ 单个设备备份</li>
              <li>✓ 基础配置对比</li>
              <li>✓ 系统设置</li>
            </ul>
          </div>
          <div class="version-card professional">
            <h4>专业版</h4>
            <ul>
              <li>✓ 所有基础版功能</li>
              <li>✓ 批量设备操作</li>
              <li>✓ 自定义命令执行</li>
              <li>✓ 高级报告生成</li>
              <li>✓ 性能分析统计</li>
              <li>✓ 自动备份调度</li>
            </ul>
          </div>
        </div>
      </div>

      <!-- 底部信息 -->
      <div class="license-footer">
        <p class="security-info">
          <icon-check-circle />
          您的授权信息将被安全加密存储，并与设备绑定
        </p>
        <p class="device-info">
          设备指纹: <code>{{ deviceFingerprint.substring(0, 16) }}...</code>
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import {
  IconCloseCircleFill,
  IconCheckCircleFill,
  IconLoading,
  IconLock,
  IconCheckCircle
} from '@arco-design/web-vue/es/icon'
import { enhancedLicenseManager, ERROR_CODES } from '@/utils/enhanced-license.js'
import { getRedirectRoute, LicenseGuardUtils } from '@/router/license-guard.js'

const router = useRouter()
const route = useRoute()

// 响应式数据
const licenseCode = ref('')
const isActivating = ref(false)
const errorMessage = ref('')
const errorCode = ref('')
const activeTab = ref('professional')

// 设备指纹
const deviceFingerprint = computed(() => {
  return enhancedLicenseManager.deviceFingerprint || ''
})

// 示例授权码
const basicCodes = [
  'BASIC-2024-NETW-TOOL',
  'DEMO-BASIC-TEST-2024',
  'BAS-2024-YCSW-VLQQ',
  'BASIC-2024-LDQN-U79T'
]

const professionalCodes = [
  'TEST-REDIRECT-2024',
  'PROF-2024-NETW-TOOL',
  'DEMO-PROF-TEST-2024',
  'PROF-2024-A1B2-C3D4',
  'PRO-2025-E5F6-G7H8'
]

// 方法
const clearError = () => {
  errorMessage.value = ''
  errorCode.value = ''
}

const selectCode = (code) => {
  licenseCode.value = code
  clearError()
}

const activateLicense = async () => {
  if (!licenseCode.value.trim()) {
    showError('请输入授权码', ERROR_CODES.EMPTY_CODE)
    return
  }

  isActivating.value = true
  clearError()

  try {
    console.log('🚀 开始授权激活流程')
    const result = await enhancedLicenseManager.activateLicense(licenseCode.value)

    if (result.success) {
      console.log('✅ 授权激活成功，触发事件:', result)

      // 显示成功状态
      showError('已验证', 'SUCCESS')

      // 触发授权成功事件 - 让事件监听器处理重定向
      window.dispatchEvent(new CustomEvent('license-activated', {
        detail: {
          type: result.type,
          message: result.message
        }
      }))

      // 不在这里直接重定向，让事件监听器处理
      console.log('🔄 等待事件监听器处理重定向...')
    } else {
      console.error('❌ 授权激活失败:', result)
      showError(result.error, result.errorCode)
    }
  } catch (error) {
    console.error('💥 授权激活过程中发生异常:', error)
    showError('授权激活过程中发生错误，请重试', ERROR_CODES.INTERNAL_ERROR)
  } finally {
    isActivating.value = false
  }
}

const showError = (message, code = '') => {
  errorMessage.value = message
  errorCode.value = code
}

// 生命周期
onMounted(() => {
  // 检查URL参数中的错误原因
  const reason = route.query.reason
  if (reason) {
    const reasonMessages = {
      'unauthorized': '您需要有效的授权才能访问此功能',
      'verification_failed': '授权验证失败，请重新输入授权码',
      'guard_error': '系统验证出现错误，请重新授权',
      'periodic_verification_failed': '定期验证失败，请重新授权',
      'visibility_check_failed': '授权状态检查失败，请重新授权'
    }
    
    if (reasonMessages[reason]) {
      showError(reasonMessages[reason])
    }
  }

  // 如果已经有有效授权，触发授权成功事件让监听器处理重定向
  if (enhancedLicenseManager.hasValidLicense()) {
    console.log('🔍 检测到已有有效授权，触发重定向事件')
    window.dispatchEvent(new CustomEvent('license-activated', {
      detail: {
        type: enhancedLicenseManager.getCurrentLicenseType(),
        message: '授权已验证'
      }
    }))
  }
})
</script>

<style scoped>
.enhanced-license-gate {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.license-container {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  max-width: 600px;
  width: 100%;
  overflow: hidden;
}

.license-header {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  color: white;
  padding: 40px 30px;
  text-align: center;
}

.logo-section .logo-icon {
  font-size: 3em;
  margin-bottom: 10px;
}

.logo-section h1 {
  margin: 0 0 10px 0;
  font-size: 1.8em;
  font-weight: 300;
}

.subtitle {
  margin: 0;
  opacity: 0.9;
  font-size: 1em;
}

.error-banner {
  background: #fff2f0;
  border: 1px solid #ffccc7;
  padding: 20px;
  display: flex;
  align-items: flex-start;
  gap: 12px;
  color: #cf1322;
}

.error-banner .arco-icon {
  font-size: 20px;
  margin-top: 2px;
}

.error-content h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
}

.error-content p {
  margin: 4px 0;
  font-size: 14px;
}

.error-code {
  font-family: monospace;
  opacity: 0.8;
}

.success-banner {
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  padding: 20px;
  display: flex;
  align-items: flex-start;
  gap: 12px;
  color: #389e0d;
}

.success-banner .arco-icon {
  font-size: 20px;
  margin-top: 2px;
}

.success-content h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
}

.success-content p {
  margin: 4px 0;
  font-size: 14px;
}

.redirect-info {
  font-style: italic;
  opacity: 0.8;
}

.license-form {
  padding: 30px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

.input-wrapper {
  position: relative;
}

.license-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e8e8e8;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.3s;
}

.license-input:focus {
  outline: none;
  border-color: #1890ff;
}

.input-loading {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #1890ff;
}

.input-hint {
  margin-top: 6px;
  font-size: 12px;
  color: #666;
}

.activate-btn {
  width: 100%;
  padding: 14px;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: background-color 0.3s;
}

.activate-btn:hover:not(:disabled) {
  background: #40a9ff;
}

.activate-btn:disabled {
  background: #d9d9d9;
  cursor: not-allowed;
}

.demo-section {
  padding: 0 30px 30px;
}

.demo-section h3 {
  margin: 0 0 20px 0;
  color: #333;
}

.demo-tabs {
  display: flex;
  margin-bottom: 20px;
  border-bottom: 1px solid #e8e8e8;
}

.tab-btn {
  padding: 10px 20px;
  border: none;
  background: none;
  cursor: pointer;
  color: #666;
  border-bottom: 2px solid transparent;
  transition: all 0.3s;
}

.tab-btn.active {
  color: #1890ff;
  border-bottom-color: #1890ff;
}

.code-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 10px;
}

.code-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
}

.code-item:hover {
  border-color: #1890ff;
  background: #f0f8ff;
}

.code-item.basic {
  border-left: 4px solid #52c41a;
}

.code-item.professional {
  border-left: 4px solid #722ed1;
}

.code-item code {
  font-family: monospace;
  font-size: 14px;
}

.copy-hint {
  font-size: 12px;
  color: #666;
}

.version-info {
  padding: 0 30px 30px;
}

.version-info h3 {
  margin: 0 0 20px 0;
  color: #333;
}

.version-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.version-card {
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
}

.version-card.basic {
  border-left: 4px solid #52c41a;
}

.version-card.professional {
  border-left: 4px solid #722ed1;
}

.version-card h4 {
  margin: 0 0 15px 0;
  color: #333;
}

.version-card ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

.version-card li {
  padding: 4px 0;
  font-size: 14px;
  color: #666;
}

.license-footer {
  background: #f8f9fa;
  padding: 20px 30px;
  text-align: center;
  border-top: 1px solid #e8e8e8;
}

.security-info {
  margin: 0 0 10px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: #52c41a;
  font-size: 14px;
}

.device-info {
  margin: 0;
  font-size: 12px;
  color: #666;
}

.device-info code {
  background: #f0f0f0;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: monospace;
}
</style>
