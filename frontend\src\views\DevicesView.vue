<template>
  <div class="device-container">
    <!-- 筛选条件区域 -->
    <div class="filter-section" :class="{ 'collapsed': filterCollapsed }">
      <a-card title="" :bordered="false" size="small">
        <div class="filter-content" v-show="!filterCollapsed">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="设备名称" :label-col-props="{ span: 24 }" :wrapper-col-props="{ span: 24 }">
                <a-input
                  v-model="filterForm.name"
                  placeholder="请输入设备名称"
                  allow-clear
                  @input="handleFilterChange"
                >
                  <template #prefix><icon-search /></template>
                </a-input>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="IP地址" :label-col-props="{ span: 24 }" :wrapper-col-props="{ span: 24 }">
                <a-input
                  v-model="filterForm.ip"
                  placeholder="请输入IP地址"
                  allow-clear
                  @input="handleFilterChange"
                >
                  <template #prefix><icon-search /></template>
                </a-input>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="设备厂商" :label-col-props="{ span: 24 }" :wrapper-col-props="{ span: 24 }">
                <a-select
                  v-model="filterForm.vendor"
                  placeholder="请选择设备厂商"
                  allow-clear
                  allow-search
                  @change="handleFilterChange"
                >
                  <a-option value="">全部</a-option>
                  <a-option
                    v-for="vendor in vendorOptions"
                    :key="vendor.value"
                    :value="vendor.value"
                  >
                    {{ vendor.label }}
                  </a-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="连接类型" :label-col-props="{ span: 24 }" :wrapper-col-props="{ span: 24 }">
                <a-select
                  v-model="filterForm.remote_type"
                  placeholder="请选择连接类型"
                  allow-clear
                  @change="handleFilterChange"
                >
                  <a-option value="">全部</a-option>
                  <a-option value="ssh">SSH</a-option>
                  <a-option value="telnet">Telnet</a-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="在线状态" :label-col-props="{ span: 24 }" :wrapper-col-props="{ span: 24 }">
                <a-select
                  v-model="filterForm.online_status"
                  placeholder="请选择在线状态"
                  allow-clear
                  @change="handleFilterChange"
                >
                  <a-option value="">全部</a-option>
                  <a-option value="online">在线</a-option>
                  <a-option value="offline">离线</a-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="备份状态" :label-col-props="{ span: 24 }" :wrapper-col-props="{ span: 24 }">
                <a-select
                  v-model="filterForm.backup_status"
                  placeholder="请选择备份状态"
                  allow-clear
                  @change="handleFilterChange"
                >
                  <a-option value="">全部</a-option>
                  <a-option value="success">备份成功</a-option>
                  <a-option value="error">备份失败</a-option>
                  <a-option value="none">未备份</a-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="操作" :label-col-props="{ span: 24 }" :wrapper-col-props="{ span: 24 }">
                <a-space>
                  <a-button type="primary" @click="applyFilters">
                    <template #icon><icon-search /></template>
                    搜索
                  </a-button>
                  <a-button @click="resetFilters">
                    <template #icon><icon-refresh /></template>
                    重置
                  </a-button>
                </a-space>
              </a-form-item>
            </a-col>
          </a-row>
        </div>

        <!-- 收起/展开按钮 -->
        <div class="filter-toggle">
          <a-button
            type="text"
            @click="toggleFilterCollapse"
            :class="{ 'filter-collapse-btn': true, 'collapsed': filterCollapsed }"
          >
            <template #icon>
              <icon-up v-if="!filterCollapsed" />
              <icon-down v-else />
            </template>
            {{ filterCollapsed ? '展开筛选' : '收起筛选' }}
          </a-button>
        </div>
      </a-card>
    </div>

    <!-- 统计信息 -->
    <div class="stats-section">
      <a-space>
        <a-tag color="blue" size="large">
          <template #icon><icon-desktop /></template>
          总设备数: {{ originalDevices.length }}
        </a-tag>
        <a-tag color="green" size="large">
          <template #icon><icon-check-circle /></template>
          筛选结果: {{ filteredDevices.length }}
        </a-tag>
        <a-tag color="orange" size="large">
          <template #icon><icon-wifi /></template>
          在线设备: {{ onlineDevicesCount }}
        </a-tag>
        <a-tag color="red" size="large">
          <template #icon><icon-close-circle /></template>
          离线设备: {{ offlineDevicesCount }}
        </a-tag>
      </a-space>
    </div>

    <div class="header-actions">
      <a-space>
        <!-- 基础功能：所有版本都可用 -->
        <a-button type="primary" @click="showAddModal">
          <template #icon><icon-plus /></template>
          添加设备
        </a-button>

        <!-- 专业版功能：导入导出 -->
        <template v-if="hasFeature(FEATURES.DEVICE_IMPORT_EXPORT)">
          <a-dropdown>
            <a-button>
              <template #icon><icon-import /></template>
              导入
            </a-button>
            <template #content>
              <a-doption @click="showImportModal('json')">
                <template #icon><icon-file /></template>
                导入JSON
              </a-doption>
              <a-doption @click="showImportModal('csv')">
                <template #icon><icon-file /></template>
                导入CSV
              </a-doption>
              <a-divider style="margin: 4px 0" />
              <a-doption @click="downloadTemplate('json')">
                <template #icon><icon-download /></template>
                下载JSON模板
              </a-doption>
              <a-doption @click="downloadTemplate('csv')">
                <template #icon><icon-download /></template>
                下载CSV模板
              </a-doption>
            </template>
          </a-dropdown>
          <a-dropdown>
            <a-button>
              <template #icon><icon-export /></template>
              导出
            </a-button>
            <template #content>
              <a-doption @click="exportDevices('json')">
                <template #icon><icon-file /></template>
                导出为JSON
              </a-doption>
              <a-doption @click="exportDevices('csv')">
                <template #icon><icon-file /></template>
                导出为CSV
              </a-doption>
            </template>
          </a-dropdown>
        </template>

        <!-- 专业版功能：批量操作 -->
        <a-button
          v-if="hasFeature(FEATURES.DEVICE_BATCH_DELETE)"
          type="primary"
          status="danger"
          :disabled="!selectedKeys.length"
          @click="handleBatchDelete"
        >
          <template #icon><icon-delete /></template>
          批量删除
        </a-button>
        <a-button
          v-if="hasFeature(FEATURES.DEVICE_BATCH_BACKUP)"
          type="primary"
          :disabled="!selectedKeys.length"
          @click="handleBatchBackup"
          :loading="batchBackupLoading"
        >
          <template #icon><icon-download /></template>
          批量备份
        </a-button>
        <a-button
          v-if="hasFeature(FEATURES.DEVICE_BATCH_INSPECTION)"
          type="primary"
          status="success"
          :disabled="!selectedKeys.length"
          @click="handleBatchInspection"
          :loading="batchInspectionLoading"
        >
          <template #icon><icon-search /></template>
          批量巡检
        </a-button>

        <!-- 专业版功能：自动备份 -->
        <a-button
          v-if="hasFeature(FEATURES.DEVICE_AUTO_BACKUP)"
          type="primary"
          status="success"
          @click="handleAutoBackup"
          :loading="autoBackupLoading"
        >
          <template #icon><icon-schedule /></template>
          自动备份
        </a-button>

        <!-- 基础功能：刷新状态 -->
        <a-button
          type="primary"
          @click="refreshDeviceStatus"
        >
          <template #icon><icon-refresh /></template>
          刷新状态
        </a-button>
      </a-space>
    </div>

    <!-- 设备选择提示信息 -->
    <div class="device-selection-info" v-if="paginatedDevices.length > 0">
      <a-alert
        type="info"
        :show-icon="false"
        style="margin-bottom: 16px;"
      >
        <template #message>
          <span>
            <icon-info-circle style="margin-right: 4px;" />
            设备列表已按在线状态排序（在线设备优先显示）。
            批量操作仅支持在线设备，离线设备无法选择。
            当前页面共 {{ paginatedDevices.length }} 个设备，
            其中 {{ getSelectableDevicesCount }} 个在线设备可选择。
          </span>
        </template>
      </a-alert>
    </div>

    <a-table
      :data="paginatedDevices"
      :loading="loading"
      :pagination="{
        pageSize: pageSize,
        current: currentPage,
        total: filteredDevices.length,
        showTotal: true,
        showPageSize: true,
        pageSizeOptions: [10, 20, 50, 100],
        showJumper: true
      }"
      @page-size-change="handlePageSizeChange"
      @page-change="handlePageChange"
      row-key="id"
      stripe
      :bordered="true"
      :cell-style="{ textAlign: 'center', verticalAlign: 'middle' }"
      :header-cell-style="{ textAlign: 'center', verticalAlign: 'middle' }"
      :row-class="(record) => record.online_status ? '' : 'offline-device'"
      v-model:selectedKeys="selectedKeys"
      :row-selection="{
        type: 'checkbox',
        showCheckedAll: true,
        width: 40,
        checkboxProps: (record) => ({
          disabled: !record.online_status
        }),
        onlyShowCheckbox: true
      }"
      @select="handleSelect"
      @select-all="handleSelectAll"
    >
      <template #columns>
        <a-table-column title="序号" :width="80">
          <template #cell="{ rowIndex }">
            {{ rowIndex + 1 }}
          </template>
        </a-table-column>
        <a-table-column title="设备名称" data-index="name" :width="150" />
        <a-table-column title="IP地址" data-index="ip" :width="150" />
        <a-table-column title="厂商" data-index="vendor" :width="120">
          <template #cell="{ record }">
            <a-tag :color="getVendorColor(record.vendor || '未知')">
              {{ getVendorName(record.vendor || '未知') }}
            </a-tag>
          </template>
        </a-table-column>
        <a-table-column title="远程类型" data-index="remote_type" :width="100">
          <template #cell="{ record }">
            <a-tag type="outline" size="small">
              {{ record.remote_type === 'ssh' ? 'SSH' : 'Telnet' }}
            </a-tag>
          </template>
        </a-table-column>
        <a-table-column title="设备状态" data-index="online_status" :width="100">
          <template #cell="{ record }">
            <a-tag :color="record.online_status ? 'green' : 'red'">
              {{ record.online_status ? '在线' : '离线' }}
            </a-tag>
          </template>
        </a-table-column>
        <a-table-column title="备份状态" data-index="backup_status" :width="100">
          <template #cell="{ record }">
            <a-tooltip v-if="record.backup_status === 'success'" content="查看备份">
              <a-tag 
                :color="getBackupStatusColor(record.backup_status)"
                :class="{ 'clickable-tag': record.backup_status === 'success' }"
                @click="record.backup_status === 'success' && navigateToBackups(record)"
              >
                {{ getBackupStatusText(record.backup_status) }}
              </a-tag>
            </a-tooltip>
            <a-tag 
              v-else 
              :color="getBackupStatusColor(record.backup_status)"
            >
              {{ getBackupStatusText(record.backup_status) }}
            </a-tag>
          </template>
        </a-table-column>
        <a-table-column title="最近备份时间" data-index="last_backup_time" :width="150">
          <template #cell="{ record }">
            {{ record.last_backup_time || '未备份' }}
          </template>
        </a-table-column>
        <a-table-column title="操作" align="center" :width="240" fixed="right">
          <template #cell="{ record }">
            <a-space size="small">
              <a-tooltip content="巡检">
                <a-button
                  type="primary"
                  status="success"
                  size="small"
                  @click="handleInspection(record)"
                  :loading="inspectionLoading[record.id]"
                  class="operation-btn"
                >
                  <template #icon><icon-search /></template>
                </a-button>
              </a-tooltip>
              <a-tooltip content="备份">
                <a-button type="primary" size="small" @click="handleBackup(record)" class="operation-btn">
                  <template #icon><icon-download /></template>
                </a-button>
              </a-tooltip>
              <a-tooltip content="查看">
                <a-button type="secondary" size="small" @click="handleDetail(record)" class="operation-btn">
                  <template #icon><icon-eye /></template>
                </a-button>
              </a-tooltip>
              <a-tooltip content="编辑">
                <a-button type="primary" status="warning" size="small" @click="handleEdit(record)" class="operation-btn">
                  <template #icon><icon-edit /></template>
                </a-button>
              </a-tooltip>
              <a-popconfirm
                content="确定要删除此设备吗?"
                @ok="handleDelete(record)"
                position="br"
              >
                <a-tooltip content="删除">
                  <a-button type="primary" status="danger" size="small" class="operation-btn">
                    <template #icon><icon-delete /></template>
                  </a-button>
                </a-tooltip>
              </a-popconfirm>
            </a-space>
          </template>
        </a-table-column>
      </template>
    </a-table>

    <a-modal
      v-model:visible="modalVisible"
      :title="isEdit ? '编辑设备' : '添加设备'"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
      :ok-loading="modalSubmitting"
    >
      <a-form ref="formRef" :model="formData" :rules="rules" layout="vertical">
                  <a-form-item field="name" label="设备名称">
            <a-input v-model="formData.name" placeholder="请输入设备名称" />
          </a-form-item>
          <a-form-item field="ip" label="IP地址">
            <a-input v-model="formData.ip" placeholder="请输入IP地址" />
          </a-form-item>
          <a-form-item field="vendor" label="设备厂商">
            <a-select
              v-model="formData.vendor"
              placeholder="请选择设备厂商"
              :filter-option="true"
              :allow-search="true"
            >
              <a-option
                v-for="vendor in vendorOptions"
                :key="vendor.value"
                :value="vendor.value"
              >
                {{ vendor.label }}
              </a-option>
            </a-select>
          </a-form-item>
          <a-form-item field="remote_type" label="远程连接类型">
            <a-radio-group v-model="formData.remote_type">
              <a-radio value="ssh">SSH</a-radio>
              <a-radio value="telnet">Telnet</a-radio>
            </a-radio-group>
          </a-form-item>
        <a-form-item field="username" label="用户名">
          <a-input v-model="formData.username" placeholder="请输入用户名" />
        </a-form-item>
        <a-form-item field="password" label="密码">
          <a-input-password v-model="formData.password" placeholder="请输入密码" />
        </a-form-item>
        <a-form-item field="enable_secret" label="特权密码">
          <a-input-password v-model="formData.enable_secret" placeholder="请输入特权密码（可选）" />
        </a-form-item>
        <a-form-item field="port" label="端口号">
          <a-input-number v-model="formData.port" :min="1" :max="65535" :default-value="22" placeholder="请输入端口号" />
        </a-form-item>
      </a-form>
    </a-modal>

    <a-modal
      v-model:visible="detailVisible"
      title="设备详情"
      @cancel="detailVisible = false"
      :footer="false"
    >
      <a-descriptions
        :data="detailData"
        :column="1"
        title="基本信息"
        layout="horizontal"
        bordered
      />
    </a-modal>

    <!-- 自动备份设置模态框 -->
    <a-modal
      v-model:visible="autoBackupModalVisible"
      title="自动备份设置"
      @cancel="autoBackupModalVisible = false"
      :footer="false"
      width="600px"
    >
      <a-form
        ref="autoBackupFormRef"
        :model="autoBackupSettings"
        layout="vertical"
      >
        <a-form-item field="auto_backup" label="自动备份">
          <a-switch
            v-model="autoBackupSettings.auto_backup"
            size="default"
            checked-text="开启"
            unchecked-text="关闭"
          >
          </a-switch>
          <div class="form-item-tip">开启后将按照设定的时间自动备份设备配置</div>
        </a-form-item>

        <a-form-item field="schedule_type" label="备份周期">
          <a-radio-group v-model="autoBackupSettings.schedule_type" :disabled="!autoBackupSettings.auto_backup">
            <a-radio value="daily">每日</a-radio>
            <a-radio value="weekly">每周</a-radio>
            <a-radio value="monthly">每月</a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item field="backup_time" label="备份时间">
          <a-time-picker
            v-model="autoBackupSettings.backup_time"
            format="HH:mm"
            :disabled="!autoBackupSettings.auto_backup"
            placeholder="选择备份时间"
            style="width: 200px"
          />
          <div class="form-item-tip">建议选择业务低峰期进行备份</div>
        </a-form-item>

        <a-form-item field="retain_days" label="保留天数">
          <a-input-number
            v-model="autoBackupSettings.retain_days"
            :min="1"
            :max="365"
            :disabled="!autoBackupSettings.auto_backup"
            placeholder="输入保留天数"
            style="width: 200px"
          />
          <div class="form-item-tip">超过保留天数的备份文件将被自动删除</div>
        </a-form-item>

        <a-form-item field="compress_backups" label="压缩备份">
          <a-switch
            v-model="autoBackupSettings.compress_backups"
            :disabled="!autoBackupSettings.auto_backup"
            size="default"
            checked-text="开启"
            unchecked-text="关闭"
          >
          </a-switch>
          <div class="form-item-tip">开启压缩可以节省存储空间</div>
        </a-form-item>

        <a-divider />

        <a-form-item>
          <a-space>
            <a-button
              type="primary"
              @click="saveAutoBackupSettings"
              :loading="autoBackupSaveLoading"
            >
              保存设置
            </a-button>
            <a-button
              type="primary"
              status="success"
              @click="executeAutoBackup"
              :loading="autoBackupExecuteLoading"
              :disabled="!autoBackupSettings.auto_backup"
            >
              <template #icon><icon-download /></template>
              执行自动备份
            </a-button>
            <a-button @click="resetAutoBackupSettings">重置设置</a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 导入设备模态框 -->
    <a-modal
      v-model:visible="importModalVisible"
      :title="`导入设备 (${importFormat === 'json' ? 'JSON' : 'CSV'})`"
      @ok="handleImport"
      @cancel="importModalVisible = false"
      :ok-loading="importLoading"
    >
      <a-upload
        :custom-request="uploadFile"
        :limit="1"
        :accept="importFormat === 'json' ? '.json' : '.csv'"
        :show-file-list="true"
        @change="handleUploadChange"
      >
        <template #upload-button>
          <a-button type="primary">
            <template #icon><icon-upload /></template>
            选择文件
          </a-button>
        </template>
        <template #file-name="{ file }">{{ file?.name || '未知文件' }}</template>
      </a-upload>
      
      <a-divider />
      
      <template v-if="importFormat === 'json'">
        <p>JSON文件格式示例:</p>
        <a-typography-paragraph code>
          [<br />
          &nbsp;&nbsp;{<br />
          &nbsp;&nbsp;&nbsp;&nbsp;"name": "设备名称",<br />
          &nbsp;&nbsp;&nbsp;&nbsp;"ip": "***********",<br />
          &nbsp;&nbsp;&nbsp;&nbsp;"device_type": "cisco_ios",<br />
          &nbsp;&nbsp;&nbsp;&nbsp;"username": "admin",<br />
          &nbsp;&nbsp;&nbsp;&nbsp;"password": "password",<br />
          &nbsp;&nbsp;&nbsp;&nbsp;"port": 22<br />
          &nbsp;&nbsp;}<br />
          ]
        </a-typography-paragraph>
      </template>
      
      <template v-else>
        <p>CSV文件格式示例:</p>
        <a-typography-paragraph code>
          name,ip,device_type,username,password,enable_secret,port<br />
          设备1,***********,cisco_ios,admin,password,,22<br />
          设备2,***********,huawei,admin,password,,22
        </a-typography-paragraph>
      </template>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, onBeforeUnmount, computed, watch, nextTick, h } from 'vue'
import { Message, Modal } from '@arco-design/web-vue'
import {
  IconPlus, IconDelete, IconEye, IconDownload, IconImport, IconExport,
  IconFile, IconUpload, IconEdit, IconRefresh, IconSearch, IconUp,
  IconDown, IconDesktop, IconCheckCircle, IconCloseCircle, IconWifi,
  IconSchedule, IconInfoCircle
} from '@arco-design/web-vue/es/icon'
import { deviceApi, settingsApi } from '../api'
import { useRouter } from 'vue-router'
import { getAllVendors, getVendorColor, getVendorDisplayName } from '@/config/vendors'
import { hasFeature, FEATURES } from '@/utils/enhanced-license'

const router = useRouter()

// 厂商配置
const vendorOptions = ref(getAllVendors())

// 加载设备列表相关变量
const originalDevices = ref([]) // 原始设备数据
const devices = ref([]) // 当前显示的设备数据（已废弃，保留兼容性）
const loading = ref(false)
const pageSize = ref(10) // 添加分页大小的响应式变量
const currentPage = ref(1) // 当前页码
const modalVisible = ref(false)
const isEdit = ref(false)
const modalSubmitting = ref(false)
const detailVisible = ref(false)
const detailData = ref([])

// 筛选相关变量
const filterForm = reactive({
  name: '',
  ip: '',
  vendor: '',
  remote_type: '',
  online_status: '',
  backup_status: ''
})

const filterCollapsed = ref(true) // 筛选区域是否收起，默认收起

// 导入相关
const importModalVisible = ref(false)
const importFormat = ref('json') // json 或 csv
const importFile = ref(null)
const importLoading = ref(false)

// 自动备份相关
const autoBackupLoading = ref(false)
const autoBackupModalVisible = ref(false)
const autoBackupSaveLoading = ref(false)
const autoBackupExecuteLoading = ref(false)
const autoBackupFormRef = ref()

// 自动备份设置
const autoBackupSettings = reactive({
  auto_backup: false,
  schedule_type: 'daily',
  backup_time: '03:00',
  retain_days: 30,
  compress_backups: true
})

// 设备类型
const deviceTypes = ref([])
const deviceTypesLoading = ref(false)

// 巡检相关
const inspectionLoading = ref({}) // 单个设备巡检加载状态
const batchInspectionLoading = ref(false) // 批量巡检加载状态
const inspectionTasks = ref([]) // 巡检任务列表
const inspectionResultModalVisible = ref(false) // 巡检结果弹窗
const currentInspectionResult = ref(null) // 当前查看的巡检结果

// 备份相关
const batchBackupLoading = ref(false) // 批量备份加载状态
const backupTasks = ref([]) // 备份任务列表

const formData = reactive({
  name: '',
  ip: '',
  device_type: '', // 自动映射，不需要用户输入
  username: '',
  password: '',
  enable_secret: '',
  port: 22,
  vendor: '', // 设备厂商
  remote_type: 'ssh' // 远程连接类型，默认ssh
})

const rules = {
  name: [{ required: true, message: '请输入设备名称' }],
  ip: [{ required: true, message: '请输入IP地址' }],
  vendor: [{ required: true, message: '请选择设备厂商' }],
  remote_type: [{ required: true, message: '请选择远程连接类型' }],
  username: [{ required: true, message: '请输入用户名' }],
  password: [{ required: true, message: '请输入密码' }]
}

const formRef = ref()

// 筛选后的设备列表
const filteredDevices = computed(() => {
  let result = originalDevices.value

  // 按设备名称筛选
  if (filterForm.name) {
    result = result.filter(device =>
      device.name && device.name.toLowerCase().includes(filterForm.name.toLowerCase())
    )
  }

  // 按IP地址筛选
  if (filterForm.ip) {
    result = result.filter(device =>
      device.ip && device.ip.includes(filterForm.ip)
    )
  }

  // 按设备厂商筛选
  if (filterForm.vendor) {
    result = result.filter(device => device.vendor === filterForm.vendor)
  }

  // 按连接类型筛选
  if (filterForm.remote_type) {
    result = result.filter(device => device.remote_type === filterForm.remote_type)
  }

  // 按在线状态筛选
  if (filterForm.online_status) {
    const isOnline = filterForm.online_status === 'online'
    result = result.filter(device => device.online_status === isOnline)
  }

  // 按备份状态筛选
  if (filterForm.backup_status) {
    if (filterForm.backup_status === 'none') {
      result = result.filter(device => !device.backup_status)
    } else {
      result = result.filter(device => device.backup_status === filterForm.backup_status)
    }
  }

  // 优化排序：在线设备优先，然后按设备名称排序
  result.sort((a, b) => {
    // 首先按在线状态排序（在线设备在前）
    if (a.online_status !== b.online_status) {
      return b.online_status - a.online_status // true(1) - false(0) = 1，在线设备排在前面
    }

    // 在线状态相同时，按设备名称排序
    const nameA = (a.name || '').toLowerCase()
    const nameB = (b.name || '').toLowerCase()
    if (nameA < nameB) return -1
    if (nameA > nameB) return 1

    // 设备名称相同时，按IP地址排序
    const ipA = a.ip || ''
    const ipB = b.ip || ''
    return ipA.localeCompare(ipB)
  })

  return result
})

// 分页后的设备列表
const paginatedDevices = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredDevices.value.slice(start, end)
})

// 在线设备数量
const onlineDevicesCount = computed(() => {
  return filteredDevices.value.filter(device => device.online_status).length
})

// 离线设备数量
const offlineDevicesCount = computed(() => {
  return filteredDevices.value.filter(device => !device.online_status).length
})

// 加载设备列表
const fetchDevices = async (checkStatus = false) => {
  try {
    loading.value = true
    const response = await deviceApi.getDevices()
    originalDevices.value = response || []

    // 初始化设备状态字段
    originalDevices.value = originalDevices.value.map(device => ({
      ...device,
      backup_status: null,
      last_backup_time: null,
      online_status: false
    }))

    // 保持向后兼容性
    devices.value = originalDevices.value
    
    // 获取设备备份状态
    try {
      // 调用实际API获取备份状态
      const backupStatus = await deviceApi.getDevicesBackupStatus() || []
      console.log("备份状态响应:", backupStatus)
      
      // 将备份状态信息添加到设备列表中
      if (Array.isArray(backupStatus)) {
        originalDevices.value = originalDevices.value.map(device => {
          const deviceBackup = backupStatus.find(item => item && item.device_id === device.id)
          if (deviceBackup) {
            return {
              ...device,
              backup_status: deviceBackup.backup_status || null,
              last_backup_time: deviceBackup.last_backup_time || null
            }
          }
          return device
        })
      }
    } catch (backupError) {
      console.error('获取备份状态失败', backupError)
      // 如果API请求失败，使用模拟数据
      originalDevices.value = originalDevices.value.map((device, index) => ({
        ...device,
        backup_status: index % 3 === 0 ? 'success' : index % 3 === 1 ? 'error' : null,
        last_backup_time: index % 3 === 0 ? '2024-07-27 12:00:00' : index % 3 === 1 ? '2024-07-26 10:30:00' : null
      }))
    }

    // 更新兼容性数据
    devices.value = originalDevices.value

    // 总是从数据库加载设备状态
    if (originalDevices.value.length > 0) {
      await loadDevicesStatusFromDB()
    }

    // 只有在明确要求检查状态时才进行实时检测
    if (checkStatus && originalDevices.value.length > 0) {
      await checkDevicesOnlineStatus()
    }
  } catch (error) {
    console.error('获取设备列表失败', error)
    Message.error('获取设备列表失败')
  } finally {
    loading.value = false
  }
}

// 从数据库加载设备在线状态（不进行实时检测）
const loadDevicesStatusFromDB = async () => {
  try {
    const allDeviceIds = originalDevices.value.map(device => device.id)

    // 分批获取状态以避免请求过大
    const batchSize = 10
    for (let i = 0; i < allDeviceIds.length; i += batchSize) {
      const batchIds = allDeviceIds.slice(i, i + batchSize)

      try {
        const statusResponse = await deviceApi.getDevicesStatusFromDB(batchIds)
        console.log(`从数据库获取批次${i}设备状态:`, statusResponse)

        if (Array.isArray(statusResponse)) {
          // 更新设备在线状态
          statusResponse.forEach(status => {
            if (status && status.device_id) {
              const deviceIndex = originalDevices.value.findIndex(d => d.id === status.device_id)
              if (deviceIndex !== -1) {
                originalDevices.value[deviceIndex].online_status = status.online
              }
            }
          })
        }
      } catch (batchError) {
        console.error(`从数据库获取批次${i}设备状态失败`, batchError)
      }
    }
  } catch (error) {
    console.error('从数据库获取设备在线状态失败', error)
  }
}

// 检查设备在线状态
const checkDevicesOnlineStatus = async () => {
  try {
    // 为避免请求过多超时，分批检查设备状态
    const batchSize = 5
    const allDeviceIds = originalDevices.value.map(device => device.id)
    
    for (let i = 0; i < allDeviceIds.length; i += batchSize) {
      const batchIds = allDeviceIds.slice(i, i + batchSize)
      
      try {
        // 尝试调用实际API获取设备状态
        const statusResponse = await deviceApi.checkDevicesStatus(batchIds)
        console.log(`批次${i}设备状态响应:`, statusResponse)
        
        if (Array.isArray(statusResponse)) {
          // 更新设备在线状态
          statusResponse.forEach(status => {
            if (status && status.device_id) {
              const deviceIndex = originalDevices.value.findIndex(d => d.id === status.device_id)
              if (deviceIndex !== -1) {
                originalDevices.value[deviceIndex].online_status = status.online
              }
            }
          })
        }
      } catch (batchError) {
        console.error(`批次${i}设备状态获取失败`, batchError)

        // 如果API失败，使用ping模拟检查设备在线状态
        for (const deviceId of batchIds) {
          const deviceIndex = originalDevices.value.findIndex(d => d.id === deviceId)
          if (deviceIndex !== -1) {
            // 模拟ping检查 - 基于IP地址的最后一位来确定，使其稳定而不是随机
            const ipLastPart = parseInt(originalDevices.value[deviceIndex].ip.split('.').pop()) || 0
            originalDevices.value[deviceIndex].online_status = (ipLastPart % 5 !== 0) // 如果IP最后一位不能被5整除，则视为在线
          }
        }
      }
    }
  } catch (error) {
    console.error('获取设备在线状态失败', error)
  }
}

// 刷新设备状态 - 进行实时检测并更新数据库
const refreshDeviceStatus = async () => {
  try {
    loading.value = true
    Message.info('正在刷新设备状态...')
    
    // 尝试获取真实的备份状态数据
    try {
      const backupStatus = await deviceApi.getDevicesBackupStatus() || []
      console.log("刷新-备份状态响应:", backupStatus)
      
      // 更新备份状态
      if (Array.isArray(backupStatus)) {
        originalDevices.value = originalDevices.value.map(device => {
          const deviceBackup = backupStatus.find(item => item && item.device_id === device.id)
          if (deviceBackup) {
            return {
              ...device,
              backup_status: deviceBackup.backup_status || null,
              last_backup_time: deviceBackup.last_backup_time || null
            }
          }
          return device
        })
        // 更新兼容性数据
        devices.value = originalDevices.value
      }
    } catch (backupError) {
      console.error('刷新-获取备份状态失败', backupError)
    }
    
    // 获取在线状态 - 手动刷新时总是检查
    await checkDevicesOnlineStatus()
    
    Message.success('设备状态刷新完成')
  } catch (error) {
    console.error('刷新设备状态失败', error)
    Message.error('刷新设备状态失败')
  } finally {
    loading.value = false
  }
}

// 定时器ID
let statusCheckTimer = null

// 设置自动检查定时器
const setupAutoStatusCheck = () => {
  // 清除可能存在的旧定时器
  if (statusCheckTimer) {
    clearInterval(statusCheckTimer)
  }
  
  // 设置新的定时器 - 每30分钟检查一次
  statusCheckTimer = setInterval(() => {
    console.log('执行自动设备状态检测...')
    checkDevicesOnlineStatus()
  }, 30 * 60 * 1000) // 30分钟
}

// 获取设备类型颜色
const getDeviceTypeColor = (type) => {
  const typeColors = {
    cisco_ios: 'blue',
    cisco_nxos: 'arcoblue',
    huawei: 'orange',
    h3c: 'green',
    ruijie: 'purple'
  }
  return typeColors[type] || 'gray'
}

// 获取设备类型名称
const getDeviceTypeName = (type) => {
  const typeNames = {
    cisco_ios: 'Cisco IOS',
    cisco_nxos: 'Cisco NX-OS',
    huawei: '华为',
    h3c: 'H3C',
    ruijie: '锐捷'
  }
  return typeNames[type] || type
}

// 获取厂商名称（使用新的配置）
const getVendorName = (vendor) => {
  return getVendorDisplayName(vendor)
}

// 获取备份状态颜色
const getBackupStatusColor = (status) => {
  if (!status) return 'gray'
  if (status === 'success') return 'green'
  if (status === 'error') return 'red'
  return 'gray'
}

// 获取备份状态文本
const getBackupStatusText = (status) => {
  if (!status) return '未备份'
  if (status === 'success') return '成功'
  if (status === 'error') return '失败'
  return '未知'
}

// 显示添加设备对话框
const showAddModal = () => {
  isEdit.value = false
  Object.keys(formData).forEach(key => {
    if (key === 'port') {
      formData[key] = 22
    } else if (key === 'remote_type') {
      formData[key] = 'ssh'
    } else {
      formData[key] = ''
    }
  })
  modalVisible.value = true
}

// 处理编辑设备
const handleEdit = (device) => {
  isEdit.value = true
  // 复制设备数据到表单
  Object.keys(formData).forEach(key => {
    formData[key] = device[key] !== null ? device[key] : ''
  })
  // 保存设备ID用于更新
  formData.id = device.id
  modalVisible.value = true
}

// 处理模态框确认
const handleModalOk = async () => {
  formRef.value.validate(async (errors) => {
    if (errors) return

    try {
      modalSubmitting.value = true
      
      if (isEdit.value) {
        // 更新设备
        await deviceApi.updateDevice(formData.id, formData)
        Message.success(`设备 ${formData.name} 更新成功`)
      } else {
        // 创建新设备
        await deviceApi.createDevice(formData)
        Message.success(`设备 ${formData.name} 添加成功`)
      }
      modalVisible.value = false
      fetchDevices() // 重新加载设备列表
    } catch (error) {
      console.error(isEdit.value ? '更新设备失败' : '添加设备失败', error)
      Message.error(`${isEdit.value ? '更新' : '添加'}设备失败: ${error.response?.data?.detail || error.message}`)
    } finally {
      modalSubmitting.value = false
    }
  })
}

// 处理模态框取消
const handleModalCancel = () => {
  modalVisible.value = false
}

// 处理设备备份 - 异步执行
const handleBackup = async (device) => {
  try {
    // 启动异步单个设备备份任务
    const response = await deviceApi.asyncSingleBackupDevice(device.id)

    if (response.success) {
      Message.success(response.message)

      // 开始轮询任务状态
      pollBackupTask(response.task_id, `设备 ${device.name}`)
    } else {
      Message.error('启动设备备份任务失败')
    }
  } catch (error) {
    console.error('设备备份失败', error)
    Message.error(`设备备份失败: ${error.message || '未知错误'}`)
  }
}

// 处理单个设备巡检
const handleInspection = async (device) => {
  try {
    // 设置加载状态
    inspectionLoading.value[device.id] = true

    // 启动巡检任务
    const response = await deviceApi.inspectSingleDevice(device.id)

    if (response.success) {
      Message.success(response.message)

      // 开始轮询任务状态
      pollInspectionTask(response.task_id, device.name)
    } else {
      Message.error('启动巡检任务失败')
    }
  } catch (error) {
    console.error('设备巡检失败', error)
    Message.error(`设备巡检失败: ${error.message || '未知错误'}`)
  } finally {
    inspectionLoading.value[device.id] = false
  }
}

// 处理批量设备巡检
const handleBatchInspection = async () => {
  try {
    if (!selectedKeys.value.length) {
      Message.warning('请先选择要巡检的设备')
      return
    }

    batchInspectionLoading.value = true

    // 启动批量巡检任务
    const response = await deviceApi.inspectBatchDevices(selectedKeys.value)

    if (response.success) {
      Message.success(response.message)

      // 开始轮询任务状态
      pollInspectionTask(response.task_id, `批量巡检(${response.total_devices}个设备)`)
    } else {
      Message.error('启动批量巡检任务失败')
    }
  } catch (error) {
    console.error('批量巡检失败', error)
    Message.error(`批量巡检失败: ${error.message || '未知错误'}`)
  } finally {
    batchInspectionLoading.value = false
  }
}

// 轮询巡检任务状态
const pollInspectionTask = async (taskId, taskName) => {
  const maxPolls = 60 // 最多轮询60次（5分钟）
  let pollCount = 0

  const poll = async () => {
    try {
      pollCount++
      const task = await deviceApi.getInspectionTask(taskId)

      if (task.status === 'completed') {
        // 任务完成
        Message.success(`${taskName} 巡检完成`)
        showInspectionResults(task)
        return
      } else if (task.status === 'failed') {
        // 任务失败
        Message.error(`${taskName} 巡检失败: ${task.error_message || '未知错误'}`)
        return
      } else if (task.status === 'running') {
        // 任务进行中，显示进度
        if (task.progress > 0) {
          Message.info(`${taskName} 进行中... ${task.progress}%`)
        }
      }

      // 继续轮询
      if (pollCount < maxPolls) {
        setTimeout(poll, 5000) // 5秒后再次轮询
      } else {
        Message.warning(`${taskName} 巡检超时，请稍后查看结果`)
      }
    } catch (error) {
      console.error('轮询巡检任务状态失败', error)
      if (pollCount < maxPolls) {
        setTimeout(poll, 5000) // 出错后继续轮询
      }
    }
  }

  // 开始轮询
  setTimeout(poll, 2000) // 2秒后开始第一次轮询
}

// 显示巡检结果
const showInspectionResults = (task) => {
  const results = task.results || []
  const successCount = results.filter(r => r.status === 'success').length
  const failedCount = results.filter(r => r.status === 'failed').length

  // 构建结果消息
  let message = `巡检完成！成功: ${successCount}个，失败: ${failedCount}个`

  // 显示详细结果
  Modal.info({
    title: '巡检结果',
    width: 800,
    content: () => {
      return h('div', [
        h('p', { style: 'margin-bottom: 16px; font-weight: bold;' }, message),
        h('div', { style: 'max-height: 400px; overflow-y: auto;' },
          results.map(result =>
            h('div', {
              key: result.device_id,
              style: `
                padding: 12px;
                margin-bottom: 8px;
                border: 1px solid ${result.status === 'success' ? '#52c41a' : '#ff4d4f'};
                border-radius: 4px;
                background-color: ${result.status === 'success' ? '#f6ffed' : '#fff2f0'};
              `
            }, [
              h('div', { style: 'font-weight: bold; margin-bottom: 4px;' },
                `${result.device_name} (${result.device_ip})`
              ),
              h('div', { style: 'color: #666; margin-bottom: 8px;' }, result.message),
              h('div', { style: 'font-size: 12px; color: #999;' },
                `时间: ${result.timestamp}`
              )
            ])
          )
        )
      ])
    }
  })
}

// 处理设备详情
const handleDetail = (device) => {
  detailData.value = [
    { label: '设备ID', value: device.id },
    { label: '设备名称', value: device.name },
    { label: 'IP地址', value: device.ip },
    { label: '设备类型', value: getDeviceTypeName(device.device_type) },
    { label: '用户名', value: device.username },
    { label: '端口', value: device.port }
  ]
  detailVisible.value = true
}

// 处理设备删除
const handleDelete = async (device) => {
  try {
    loading.value = true
    await deviceApi.deleteDevice(device.id)
    Message.success(`设备 ${device.name} 删除成功`)
    fetchDevices() // 重新加载设备列表
  } catch (error) {
    console.error('设备删除失败', error)
    Message.error(`设备删除失败: ${error.message}`)
  } finally {
    loading.value = false
  }
}

// 加载设备类型
const fetchDeviceTypes = async () => {
  try {
    deviceTypesLoading.value = true
    const response = await deviceApi.getDeviceTypes()
    deviceTypes.value = response || []
  } catch (error) {
    console.error('获取设备类型失败', error)
    Message.error('获取设备类型失败')
  } finally {
    deviceTypesLoading.value = false
  }
}

// 显示导入模态框
const showImportModal = (format) => {
  importFormat.value = format
  importFile.value = null
  importModalVisible.value = true
}

// 处理文件上传
const uploadFile = ({ file }) => {
  try {
    // 自定义上传，只保存文件对象，不实际上传
    if (file) {
      importFile.value = file
    }
  } catch (error) {
    console.error('处理文件上传失败', error)
    Message.error('文件处理失败')
    importFile.value = null
  }
}

// 处理上传变更
const handleUploadChange = (files) => {
  try {
    importFile.value = files && files.length > 0 && files[0] && files[0].file ? files[0].file : null
  } catch (error) {
    console.error('处理上传文件变更失败', error)
    importFile.value = null
  }
}

// 执行导入
const handleImport = async () => {
  if (!importFile.value) {
    Message.warning('请先选择文件')
    return
  }
  
  try {
    importLoading.value = true
    
    let response
    if (importFormat.value === 'json') {
      response = await deviceApi.importDevicesJSON(importFile.value)
    } else {
      response = await deviceApi.importDevicesCSV(importFile.value)
    }
    
    // 检查是否有警告信息需要显示
    if (response && response.warnings && response.warnings.length > 0) {
      // 显示部分成功导入的消息
      Message.warning({
        content: response.message,
        duration: 5000
      })
      
      // 显示详细警告
      Modal.warning({
        title: '导入警告',
        content: `
          <div style="max-height: 300px; overflow-y: auto;">
            <p>以下行导入失败：</p>
            <ul>
              ${response.warnings.map(warning => `<li style="margin-bottom: 8px;">${warning}</li>`).join('')}
            </ul>
          </div>
        `
      })
    } else {
      Message.success(response?.message || '导入成功')
    }
    
    importModalVisible.value = false
    fetchDevices()
  } catch (error) {
    console.error('导入失败', error)
    
    // 提取错误信息
    let errorMessage = '导入失败'
    if (error.response?.data?.detail) {
      errorMessage = error.response.data.detail
    } else if (error.message) {
      errorMessage = error.message
    }
    
    // 显示错误对话框，支持长文本
    Modal.error({
      title: '导入失败',
      content: errorMessage,
      okText: '确定'
    })
  } finally {
    importLoading.value = false
  }
}

// 下载导入模板
const downloadTemplate = async (format) => {
  try {
    const result = await deviceApi.getImportTemplate(format)
    
    // 创建Blob并下载
    const blob = new Blob([result.content], { 
      type: format === 'json' ? 'application/json' : 'text/csv' 
    })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = result.filename
    document.body.appendChild(a)
    a.click()
    window.URL.revokeObjectURL(url)
    document.body.removeChild(a)
    
    Message.success(`${format.toUpperCase()}模板已下载`)
  } catch (error) {
    console.error('下载模板失败', error)
    Message.error('下载模板失败')
  }
}

// 导出设备
const exportDevices = async (format) => {
  try {
    if (format === 'json') {
      await deviceApi.exportDevicesJSON()
    } else {
      await deviceApi.exportDevicesCSV()
    }
  } catch (error) {
    console.error('导出失败', error)
    Message.error(`导出失败: ${error.message}`)
  }
}

// 多选框相关
const selectedKeys = ref([])

// 处理单个设备选择
const handleSelect = (rowKeys, rowKey, record) => {
  console.log('handleSelect called:', { rowKeys, rowKey, record })

  // 始终过滤掉离线设备，确保selectedKeys中只包含在线设备
  const onlineKeys = rowKeys.filter(key => {
    const device = paginatedDevices.value.find(d => d.id === key)
    return device && device.online_status
  })

  // 如果尝试选择离线设备，显示警告
  if (record && !record.online_status) {
    Message.warning('只能选择在线设备进行操作')
  }

  // 更新选择状态，只保留在线设备
  selectedKeys.value = [...onlineKeys]

  // 使用nextTick确保UI更新
  nextTick(() => {
    console.log('UI updated, selectedKeys:', selectedKeys.value)
    // 强制重新渲染表格
    selectedKeys.value = [...selectedKeys.value]
  })
}

// 处理全选
const handleSelectAll = (checked) => {
  console.log('handleSelectAll called:', { checked })
  if (checked) {
    // 全选时只选择在线设备
    const onlineDevices = paginatedDevices.value.filter(device => device.online_status)
    selectedKeys.value = onlineDevices.map(device => device.id)
  } else {
    // 取消全选
    selectedKeys.value = []
  }
}

// 监听设备列表变化，自动清理离线设备的选择
watch([paginatedDevices], () => {
  // 当设备列表变化时，确保selectedKeys中只包含在线设备
  const onlineKeys = selectedKeys.value.filter(key => {
    const device = paginatedDevices.value.find(d => d.id === key)
    return device && device.online_status
  })

  if (onlineKeys.length !== selectedKeys.value.length) {
    selectedKeys.value = onlineKeys
  }
}, { deep: true })

// 获取可选择的在线设备数量
const getSelectableDevicesCount = computed(() => {
  return paginatedDevices.value.filter(device => device.online_status).length
})

// 确保选择的设备都是在线的
const validSelectedKeys = computed({
  get() {
    return selectedKeys.value.filter(key => {
      const device = paginatedDevices.value.find(d => d.id === key)
      return device && device.online_status
    })
  },
  set(newKeys) {
    // 只允许设置在线设备的ID
    const onlineKeys = newKeys.filter(key => {
      const device = paginatedDevices.value.find(d => d.id === key)
      return device && device.online_status
    })
    selectedKeys.value = onlineKeys
  }
})

// 批量删除
const handleBatchDelete = async () => {
  if (!selectedKeys.value.length) {
    Message.warning('请选择要删除的设备')
    return
  }

  Modal.confirm({
    title: '批量删除',
    content: `确定要删除选中的 ${selectedKeys.value.length} 个设备吗？`,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        loading.value = true
        const res = await deviceApi.batchDeleteDevices(selectedKeys.value)
        Message.success(`已删除 ${res.success_count} 个设备`)
        
        if (res.failed_count > 0) {
          Modal.error({
            title: '部分设备删除失败',
            content: res.failed_devices.map(d => `${d.id}: ${d.error}`).join('<br>'),
          })
        }
        
        fetchDevices()
        selectedKeys.value = []
      } catch (error) {
        console.error('批量删除失败', error)
        Message.error(`批量删除失败: ${error.message}`)
      } finally {
        loading.value = false
      }
    }
  })
}

// 批量备份 - 异步执行
const handleBatchBackup = async () => {
  try {
    if (!selectedKeys.value.length) {
      Message.warning('请先选择要备份的设备')
      return
    }

    batchBackupLoading.value = true

    // 启动异步批量备份任务
    const response = await deviceApi.asyncBatchBackupDevices(selectedKeys.value)

    if (response.success) {
      Message.success(response.message)

      // 开始轮询任务状态
      pollBackupTask(response.task_id, `批量备份(${response.total_devices}个设备)`)
    } else {
      Message.error('启动批量备份任务失败')
    }
  } catch (error) {
    console.error('批量备份失败', error)
    Message.error(`批量备份失败: ${error.message || '未知错误'}`)
  } finally {
    batchBackupLoading.value = false
  }
}

// 轮询备份任务状态
const pollBackupTask = async (taskId, taskName) => {
  const maxPolls = 60 // 最多轮询60次（5分钟）
  let pollCount = 0

  const poll = async () => {
    try {
      pollCount++
      const task = await deviceApi.getBackupTask(taskId)

      if (task.status === 'completed') {
        // 任务完成
        Message.success(`${taskName} 备份完成`)
        showBackupResults(task)
        // 刷新设备列表以更新备份状态
        fetchDevices()
        return
      } else if (task.status === 'failed') {
        // 任务失败
        Message.error(`${taskName} 备份失败: ${task.error_message || '未知错误'}`)
        return
      } else if (task.status === 'running') {
        // 任务进行中，显示进度
        if (task.progress > 0) {
          Message.info(`${taskName} 进行中... ${task.progress}%`)
        }
      }

      // 继续轮询
      if (pollCount < maxPolls) {
        setTimeout(poll, 5000) // 5秒后再次轮询
      } else {
        Message.warning(`${taskName} 备份超时，请稍后查看结果`)
      }
    } catch (error) {
      console.error('轮询备份任务状态失败', error)
      if (pollCount < maxPolls) {
        setTimeout(poll, 5000) // 出错后继续轮询
      }
    }
  }

  // 开始轮询
  setTimeout(poll, 2000) // 2秒后开始第一次轮询
}

// 显示备份结果
const showBackupResults = (task) => {
  const results = task.results || []
  const successCount = results.filter(r => r.status === 'success').length
  const failedCount = results.filter(r => r.status === 'failed').length

  // 构建结果消息
  let message = `备份完成！成功: ${successCount}个，失败: ${failedCount}个`

  // 显示详细结果
  Modal.info({
    title: '备份结果',
    width: 800,
    content: () => {
      return h('div', [
        h('p', { style: 'margin-bottom: 16px; font-weight: bold;' }, message),
        h('div', { style: 'max-height: 400px; overflow-y: auto;' },
          results.map(result =>
            h('div', {
              key: result.device_id,
              style: `
                padding: 12px;
                margin-bottom: 8px;
                border: 1px solid ${result.status === 'success' ? '#52c41a' : '#ff4d4f'};
                border-radius: 4px;
                background-color: ${result.status === 'success' ? '#f6ffed' : '#fff2f0'};
              `
            }, [
              h('div', { style: 'font-weight: bold; margin-bottom: 4px;' },
                `${result.device_name} (${result.device_ip})`
              ),
              h('div', { style: 'color: #666; margin-bottom: 8px;' }, result.message),
              result.filename && h('div', { style: 'font-size: 12px; color: #1890ff; margin-bottom: 4px;' },
                `文件: ${result.filename}`
              ),
              h('div', { style: 'font-size: 12px; color: #999;' },
                `时间: ${result.timestamp}`
              )
            ])
          )
        )
      ])
    }
  })
}

// 自动备份 - 打开设置模态框
const handleAutoBackup = async () => {
  try {
    autoBackupLoading.value = true

    // 加载当前的备份设置到模态框
    await loadAutoBackupSettings()

    // 显示模态框
    autoBackupModalVisible.value = true
  } catch (error) {
    console.error('打开自动备份设置失败', error)
    Message.error('打开自动备份设置失败')
  } finally {
    autoBackupLoading.value = false
  }
}

// 加载自动备份设置
const loadAutoBackupSettings = async () => {
  try {
    // 首先尝试从API获取设置
    try {
      const backupSettings = await settingsApi.getBackupSettings()
      console.log('从API获取备份设置:', backupSettings)
      Object.assign(autoBackupSettings, backupSettings)
    } catch (apiError) {
      console.warn('API获取备份设置失败，使用本地设置:', apiError)
      // 如果API失败，从localStorage获取
      const savedSettings = localStorage.getItem('backup-settings')
      if (savedSettings) {
        Object.assign(autoBackupSettings, JSON.parse(savedSettings))
      }
    }
  } catch (error) {
    console.error('加载备份设置失败', error)
  }
}

// 保存自动备份设置
const saveAutoBackupSettings = async () => {
  try {
    autoBackupSaveLoading.value = true

    // 手动验证
    if (autoBackupSettings.auto_backup) {
      if (!autoBackupSettings.retain_days ||
          typeof autoBackupSettings.retain_days !== 'number' ||
          autoBackupSettings.retain_days < 1 ||
          autoBackupSettings.retain_days > 365) {
        Message.error('保留天数必须在1-365之间')
        return
      }

      if (!autoBackupSettings.backup_time) {
        Message.error('请选择备份时间')
        return
      }

      if (!autoBackupSettings.schedule_type) {
        Message.error('请选择调度类型')
        return
      }
    }

    // 保存到localStorage
    localStorage.setItem('backup-settings', JSON.stringify(autoBackupSettings))

    // 尝试保存到后端API
    try {
      await settingsApi.saveBackupSettings(autoBackupSettings)
      Message.success('备份设置已保存到服务器')
    } catch (apiError) {
      console.error('API保存失败:', apiError)
      Message.success('备份设置已保存到本地（API连接失败）')
    }
  } catch (error) {
    console.error('保存备份设置失败', error)
    Message.error('保存备份设置失败')
  } finally {
    autoBackupSaveLoading.value = false
  }
}

// 执行自动备份
const executeAutoBackup = async () => {
  try {
    autoBackupExecuteLoading.value = true

    // 检查自动备份是否启用
    if (!autoBackupSettings.auto_backup) {
      Message.warning('自动备份功能未启用，请先开启自动备份')
      return
    }

    // 筛选出在线的设备
    const onlineDevices = originalDevices.value.filter(device => device.online_status)

    if (onlineDevices.length === 0) {
      Message.warning('当前没有在线设备可以备份')
      return
    }

    // 确认对话框
    Modal.confirm({
      title: '自动备份确认',
      content: `将对 ${onlineDevices.length} 个在线设备执行自动备份，是否继续？`,
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          const onlineDeviceIds = onlineDevices.map(device => device.id)
          const res = await deviceApi.batchBackupDevices(onlineDeviceIds)

          Message.success(`自动备份完成：成功备份 ${res.success_count} 个在线设备`)

          if (res.failed_count > 0) {
            Modal.error({
              title: '部分设备自动备份失败',
              content: res.failed_devices.map(d => `${d.id}: ${d.error}`).join('<br>'),
            })
          }

          // 关闭模态框
          autoBackupModalVisible.value = false

          // 刷新设备列表以更新备份状态
          fetchDevices()
        } catch (error) {
          console.error('自动备份失败', error)
          Message.error(`自动备份失败: ${error.message}`)
        }
      }
    })
  } catch (error) {
    console.error('执行自动备份失败', error)
    Message.error('执行自动备份失败')
  } finally {
    autoBackupExecuteLoading.value = false
  }
}

// 重置自动备份设置
const resetAutoBackupSettings = () => {
  Object.assign(autoBackupSettings, {
    auto_backup: false,
    schedule_type: 'daily',
    backup_time: '03:00',
    retain_days: 30,
    compress_backups: true
  })
  Message.success('设置已重置')
}

// 导航到备份详情页面
const navigateToBackups = (device) => {
  router.push(`/device-backups/${device.id}`)
}

// 筛选相关函数
const handleFilterChange = () => {
  // 实时筛选，重置到第一页
  currentPage.value = 1
}

const applyFilters = () => {
  // 手动触发筛选，重置到第一页
  currentPage.value = 1
  Message.success(`筛选完成，共找到 ${filteredDevices.value.length} 个设备`)
}

const resetFilters = () => {
  // 重置所有筛选条件
  Object.keys(filterForm).forEach(key => {
    filterForm[key] = ''
  })
  currentPage.value = 1
  Message.info('筛选条件已重置')
}

const toggleFilterCollapse = () => {
  filterCollapsed.value = !filterCollapsed.value
}

// 处理分页变化
const handlePageChange = (page) => {
  currentPage.value = page
}

// 处理每页显示数量变化
const handlePageSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1 // 重置到第一页
}

// 从localStorage恢复筛选条件
const restoreFilters = () => {
  try {
    // 如果筛选区域默认收起，则不恢复筛选条件，保持清空状态
    if (filterCollapsed.value) {
      return
    }

    const savedFilters = localStorage.getItem('device-filters')
    if (savedFilters) {
      const filters = JSON.parse(savedFilters)
      Object.keys(filterForm).forEach(key => {
        if (filters[key] !== undefined) {
          filterForm[key] = filters[key]
        }
      })
    }
  } catch (error) {
    console.error('恢复筛选条件失败:', error)
  }
}

// 保存筛选条件到localStorage
const saveFilters = () => {
  try {
    localStorage.setItem('device-filters', JSON.stringify(filterForm))
  } catch (error) {
    console.error('保存筛选条件失败:', error)
  }
}

// 监听筛选条件变化，自动保存
watch(filterForm, saveFilters, { deep: true })

onMounted(() => {
  fetchDevices(false) // 初始加载时不检查设备在线状态
  fetchDeviceTypes()
  setupAutoStatusCheck() // 设置自动检查定时器
  restoreFilters() // 恢复筛选条件
})

// 组件销毁时清除定时器
onBeforeUnmount(() => {
  if (statusCheckTimer) {
    clearInterval(statusCheckTimer)
  }
})
</script>

<style scoped>
.device-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.filter-section {
  margin-bottom: 16px;
  transition: all 0.3s ease;
}

.filter-section.collapsed {
  margin-bottom: 8px;
}

.filter-section :deep(.arco-card-body) {
  padding: 16px;
}

.filter-section.collapsed :deep(.arco-card-body) {
  padding: 8px 16px;
}

.filter-content {
  transition: all 0.3s ease;
}

.filter-toggle {
  text-align: center;
  margin-top: 8px;
  border-top: 1px solid var(--color-border-2);
  padding-top: 8px;
}

.filter-section :deep(.arco-form-item) {
  margin-bottom: 16px;
}

.filter-section :deep(.arco-form-item-label) {
  font-weight: 500;
  color: var(--color-text-1);
}

.filter-collapse-btn {
  color: var(--color-text-2);
  transition: color 0.3s ease;
}

.filter-collapse-btn.collapsed {
  color: var(--color-primary);
}

.filter-collapse-btn:hover {
  color: var(--color-primary);
}

.stats-section {
  margin-bottom: 16px;
  padding: 12px 16px;
  background: var(--color-fill-1);
  border-radius: 6px;
}

.stats-section :deep(.arco-tag) {
  margin-right: 12px;
  font-weight: 500;
}

.header-actions {
  margin-bottom: 16px;
  display: flex;
  justify-content: flex-end;
}

/* 表格容器样式 */
:deep(.arco-table-container) {
  flex: 1;
  overflow: auto;
}

:deep(.arco-pagination) {
  margin-top: 16px;
}

/* 表格相关样式 */
:deep(.arco-table-cell) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

:deep(.arco-table-th) {
  font-weight: bold;
  background-color: var(--color-fill-2);
}

:deep(.arco-tag) {
  display: inline-block;
  white-space: nowrap;
}

/* 操作列按钮样式 */
.operations-container {
  width: 100%;
}

.operations-row {
  display: flex;
  justify-content: center;
  gap: 8px;
}

:deep(.operations-row .arco-btn) {
  padding: 0 8px;
}

/* 操作按钮图标大小调整 */
:deep(.operation-btn) {
  min-width: 30px;
  height: 30px;
}

:deep(.operation-btn .arco-icon) {
  font-size: 16px !important;
}

:deep(.operation-btn .arco-btn-icon-only) {
  padding: 0 6px;
}

/* 可点击标签样式 */
.clickable-tag {
  cursor: pointer;
  color: var(--color-primary);
  text-decoration: underline;
}

/* 自动备份模态框样式 */
.form-item-tip {
  font-size: 12px;
  color: var(--color-text-3);
  margin-top: 4px;
  line-height: 1.4;
}

/* 自动备份模态框表单样式 */
:deep(.arco-modal .arco-form-item) {
  margin-bottom: 20px;
}

:deep(.arco-modal .arco-switch) {
  min-width: 44px;
  height: 22px;
}

:deep(.arco-modal .arco-switch-button) {
  width: 18px;
  height: 18px;
}

:deep(.arco-modal .arco-switch-text) {
  font-size: 12px;
  font-weight: 500;
}

/* 设备选择提示信息样式 */
.device-selection-info {
  margin-bottom: 16px;
}

/* 离线设备行样式 */
:deep(.arco-table-tr.offline-device) {
  background-color: rgba(245, 245, 245, 0.5);
  color: #999;

  .arco-checkbox {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .arco-table-td {
    opacity: 0.7;
  }
}

/* 禁用的复选框样式 */
:deep(.arco-checkbox-disabled) {
  .arco-checkbox-icon {
    background-color: #f5f5f5;
    border-color: #d9d9d9;
    cursor: not-allowed;
  }
}
</style>