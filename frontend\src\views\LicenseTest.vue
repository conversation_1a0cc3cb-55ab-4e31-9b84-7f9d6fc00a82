<template>
  <div class="license-test">
    <a-card title="授权系统测试">
      <!-- 当前授权状态 -->
      <div class="license-status">
        <h3>当前授权状态</h3>
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="版本类型">
            <a-tag :color="isProfessional ? 'green' : 'blue'">
              {{ isProfessional ? '专业版' : '基础版' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="授权码">
            {{ licenseInfo.code || '未激活' }}
          </a-descriptions-item>
          <a-descriptions-item label="激活时间">
            {{ licenseInfo.activatedAt ? new Date(licenseInfo.activatedAt).toLocaleString() : '未激活' }}
          </a-descriptions-item>
          <a-descriptions-item label="可用功能数">
            {{ licenseInfo.features.length }}
          </a-descriptions-item>
        </a-descriptions>
      </div>

      <!-- 功能权限测试 -->
      <div class="feature-test" style="margin-top: 24px;">
        <h3>功能权限测试</h3>
        <a-space direction="vertical" style="width: 100%;">
          <div v-for="(feature, key) in FEATURES" :key="key" class="feature-item">
            <a-row align="middle">
              <a-col :span="12">
                <span>{{ FEATURE_DESCRIPTIONS[feature] }}</span>
              </a-col>
              <a-col :span="6">
                <a-tag :color="hasFeature(feature) ? 'green' : 'red'">
                  {{ hasFeature(feature) ? '已授权' : '未授权' }}
                </a-tag>
              </a-col>
              <a-col :span="6">
                <a-button 
                  size="small" 
                  :disabled="!hasFeature(feature)"
                  @click="testFeature(feature)"
                >
                  测试功能
                </a-button>
              </a-col>
            </a-row>
          </div>
        </a-space>
      </div>

      <!-- 授权操作 -->
      <div class="license-actions" style="margin-top: 24px;">
        <h3>授权操作</h3>
        <a-space>
          <a-button type="primary" @click="showUpgradeModal" v-if="!isProfessional">
            升级到专业版
          </a-button>
          <a-button @click="showComparisonModal">
            查看版本对比
          </a-button>
          <a-button status="danger" @click="resetLicense" v-if="isProfessional">
            重置为基础版
          </a-button>
          <a-button @click="refreshStatus">
            刷新状态
          </a-button>
        </a-space>
      </div>

      <!-- 演示授权码 -->
      <div class="demo-codes" style="margin-top: 24px;">
        <h3>演示授权码</h3>
        <a-alert type="info" show-icon>
          <div>
            <p><strong>专业版授权码：</strong></p>
            <ul>
              <li><code>PROF-2024-NETW-TOOL</code></li>
              <li><code>DEMO-PROF-TEST-2024</code></li>
            </ul>
            <p>点击"升级到专业版"按钮，输入上述任一授权码即可激活专业版功能。</p>
          </div>
        </a-alert>
      </div>
    </a-card>

    <!-- 升级弹窗 -->
    <LicenseUpgrade 
      v-model="upgradeModalVisible" 
      @success="handleUpgradeSuccess"
    />

    <!-- 版本对比弹窗 -->
    <VersionComparison 
      v-model="comparisonModalVisible"
      :show-success-message="showUpgradeSuccess"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { Message } from '@arco-design/web-vue'
import {
  isProfessional,
  hasFeature,
  FEATURES,
  FEATURE_DESCRIPTIONS,
  getLicenseInfo,
  enhancedLicenseManager
} from '@/utils/enhanced-license'
import LicenseUpgrade from '@/components/LicenseUpgrade.vue'
import VersionComparison from '@/components/VersionComparison.vue'

// 响应式数据
const upgradeModalVisible = ref(false)
const comparisonModalVisible = ref(false)
const showUpgradeSuccess = ref(false)
const licenseInfo = ref({})

// 计算属性
const currentLicenseType = computed(() => {
  return isProfessional.value ? '专业版' : '基础版'
})

// 方法
const refreshStatus = () => {
  licenseInfo.value = getLicenseInfo()
  Message.success('状态已刷新')
}

const testFeature = (feature) => {
  if (hasFeature(feature)) {
    Message.success(`功能 "${FEATURE_DESCRIPTIONS[feature]}" 测试成功！`)
  } else {
    Message.error(`功能 "${FEATURE_DESCRIPTIONS[feature]}" 未授权！`)
  }
}

const showUpgradeModal = () => {
  upgradeModalVisible.value = true
}

const showComparisonModal = () => {
  comparisonModalVisible.value = true
}

const handleUpgradeSuccess = (result) => {
  showUpgradeSuccess.value = true
  comparisonModalVisible.value = true
  refreshStatus()
  
  // 3秒后隐藏成功消息
  setTimeout(() => {
    showUpgradeSuccess.value = false
  }, 3000)
}

const resetLicense = () => {
  enhancedLicenseManager.clearLicense()
  refreshStatus()
  Message.success('已重置为基础版')
}

// 生命周期
onMounted(() => {
  refreshStatus()
})
</script>

<style scoped>
.license-test {
  padding: 20px;
}

.feature-item {
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.feature-item:last-child {
  border-bottom: none;
}

.demo-codes code {
  background: #f6f8fa;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
}

.demo-codes ul {
  margin: 8px 0;
  padding-left: 20px;
}

.demo-codes li {
  margin: 4px 0;
}
</style>
